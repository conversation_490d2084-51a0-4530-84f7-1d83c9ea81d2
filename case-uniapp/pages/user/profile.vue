<template>
  <view class="container">
    <!-- 用户信息 -->
    <view class="user-info-section" v-if="userInfo">
      <view class="user-header">
        <image class="user-avatar-large" :src="processAvatarUrl(userInfo.avatar)" mode="aspectFill"></image>
        <view class="user-details">
          <text class="user-name-large">{{ userInfo.nickName }}</text>
          <text class="user-email" v-if="userInfo.email">{{ userInfo.email }}</text>
          <text class="user-phone" v-if="userInfo.phone">{{ userInfo.phone }}</text>
        </view>
      </view>

      <view class="user-stats">
        <view class="stat-item">
          <text class="stat-number">{{ userCases.length }}</text>
          <text class="stat-label">发布案例</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ totalViews }}</text>
          <text class="stat-label">总浏览量</text>
        </view>
      </view>
    </view>

    <!-- 案例列表 -->
    <view class="cases-section">
      <view class="section-header">
        <text class="section-title">发布的案例</text>
        <text class="case-count">共{{ userCases.length }}个</text>
      </view>

      <view class="case-list" v-if="userCases.length > 0">
        <view class="case-item" v-for="caseItem in userCases" :key="caseItem.caseId">
          <!-- 用户信息 -->
          <view class="case-user" @click="goToUserProfile(caseItem.publisherId)">
            <image class="user-avatar-small" :src="processAvatarUrl(caseItem.publisher.avatar)" mode="aspectFill">
            </image>
            <view class="user-info-small">
              <text class="user-name-small">{{ caseItem.publisher.nickName }}</text>
              <text class="publish-time">{{ formatTime(caseItem.createTime) }}</text>
            </view>
          </view>

          <!-- 案例标签 -->
          <view class="case-tags" v-if="caseItem.caseTags">
            <text class="tag" v-for="tag in processTags(caseItem.caseTags)" :key="tag">#{{ tag }}</text>
          </view>

          <!-- 案例标题 -->
          <view class="case-title" @click="goToCaseDetail(caseItem.caseId)">
            {{ caseItem.caseTitle }}
          </view>

          <!-- 案例内容 -->
          <view class="case-content">
            <view class="content-text" :class="{ expanded: expandedCases[caseItem.caseId] }">
              {{ processCaseContent(caseItem.caseContent, expandedCases[caseItem.caseId] ? 1000 : 150) }}
            </view>
            <text class="expand-btn" v-if="processCaseContent(caseItem.caseContent, 1000).length > 150"
              @click="toggleExpand(caseItem.caseId)">
              {{ expandedCases[caseItem.caseId] ? '收起' : '全文' }}
            </text>
          </view>

          <!-- 案例图片 -->
          <view class="case-images" v-if="caseItem.caseImages">
            <view class="image-grid">
              <view class="image-item" v-for="(image, index) in processImages(caseItem.caseImages).slice(0, 3)"
                :key="index" @click="openImageViewer(processImages(caseItem.caseImages), index)">
                <image class="case-image" :src="image" mode="aspectFill"></image>
                <!-- 显示剩余图片数量 -->
                <view class="more-images" v-if="index === 2 && processImages(caseItem.caseImages).length > 3">
                  <text class="more-count">+{{ processImages(caseItem.caseImages).length - 3 }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 案例操作 -->
          <view class="case-actions">
            <view class="action-item" @click="goToCaseDetail(caseItem.caseId)">
              <text class="action-text">查看详情</text>
            </view>
            <view class="action-item">
              <text class="click-count">{{ caseItem.clickCount }} 次查看</text>
            </view>
          </view>
        </view>
      </view>



      <!-- 空状态 -->
      <view class="empty-state" v-else-if="userCases.length === 0 && !loading">
        <image class="empty-image" src="/static/empty-case.png" mode="aspectFit"></image>
        <text class="empty-text">暂无发布的案例</text>
      </view>
    </view>

    <!-- 图片查看器 -->
    <ImageViewer :visible="imageViewerVisible" :images="currentImages" :initialIndex="currentImageIndex"
      @close="closeImageViewer" @change="onImageChange" />

    <!-- 加载中 -->
    <view class="loading" v-if="loading">
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>

<script>
import { caseUserApi, caseInfoApi } from '@/utils/api.js'
import { formatTime, processImages, processAvatarUrl, processTags, processCaseContent, previewImages } from '@/utils/utils.js'
import ImageViewer from '@/components/ImageViewer.vue'

export default {
  components: {
    ImageViewer
  },
  data() {
    return {
      userId: null,
      userInfo: null,
      userCases: [],
      loading: false,
      // 展开的案例内容
      expandedCases: {},
      // 图片查看器相关
      imageViewerVisible: false,
      currentImages: [],
      currentImageIndex: 0
    }
  },

  computed: {
    // 计算总浏览量
    totalViews() {
      return this.userCases.reduce((total, caseItem) => total + (caseItem.clickCount || 0), 0)
    }
  },

  onLoad(options) {
    this.userId = options.userId
    if (this.userId) {
      this.loadUserData()
    }
  },

  onPullDownRefresh() {
    this.refreshData().then(() => {
      uni.stopPullDownRefresh()
    })
  },



  methods: {
    // 加载用户数据
    async loadUserData() {
      this.loading = true
      try {
        // 先加载用户信息，再加载用户案例（因为案例需要用到用户信息）
        await this.loadUserInfo()
        await this.loadUserCases()
      } catch (error) {
        console.error('加载用户数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 刷新数据
    async refreshData() {
      this.userCases = []
      this.expandedCases = {}
      await this.loadUserData()
    },

    // 加载用户信息
    async loadUserInfo() {
      try {
        const res = await caseUserApi.getUserDetail(this.userId)
        this.userInfo = res.data

        // 设置页面标题
        uni.setNavigationBarTitle({
          title: this.userInfo.nickName + '的主页'
        })
      } catch (error) {
        console.error('加载用户信息失败:', error)
        uni.showToast({
          title: '用户不存在',
          icon: 'none'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    },

    // 加载用户案例
    async loadUserCases() {
    

      try {
        // 一次性获取所有案例，不使用分页
        const res = await caseInfoApi.getCasesByPublisher(this.userId, 1, 1000)
        const cases = res.data || []

        // 为每个案例添加发布者信息
        const processedCases = cases.map(caseItem => ({
          ...caseItem,
          publisher: this.userInfo || {
            nickName: '未知用户',
            avatar: ''
          },
          publisherId: this.userId
        }))

        this.userCases = processedCases

        console.log(`加载用户案例完成，获取数据量: ${cases.length}`)
      } catch (error) {
        console.error('加载用户案例失败:', error)
        this.userCases = []
        throw error // 重新抛出错误，让调用方处理
      } finally {
      }
    },

    // 切换内容展开状态
    toggleExpand(caseId) {
      this.$set(this.expandedCases, caseId, !this.expandedCases[caseId])
    },

    // 跳转到用户主页
    goToUserProfile(userId) {
      uni.navigateTo({
        url: `/pages/user/profile?userId=${userId}`
      })
    },

    // 跳转到案例详情
    goToCaseDetail(caseId) {
      uni.navigateTo({
        url: `/pages/case/detail?caseId=${caseId}`
      })
    },

    // 打开图片查看器
    openImageViewer(images, index = 0) {
      this.currentImages = images
      this.currentImageIndex = index
      this.imageViewerVisible = true
    },

    // 关闭图片查看器
    closeImageViewer() {
      this.imageViewerVisible = false
      this.currentImages = []
      this.currentImageIndex = 0
    },

    // 图片切换事件
    onImageChange(index) {
      this.currentImageIndex = index
    },

    // 工具方法
    formatTime,
    processImages,
    processAvatarUrl,
    processTags,
    processCaseContent,
    previewImages
  }
}
</script>

<style scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 用户信息区域 */
.user-info-section {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-avatar-large {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
}

.user-details {
  flex: 1;
}

.user-name-large {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.user-email,
.user-phone {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 5rpx;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 40rpx;
  font-weight: bold;
  color: #007AFF;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 案例列表区域 */
.cases-section {
  background-color: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.case-count {
  font-size: 26rpx;
  color: #999;
}

/* 案例列表 */
.case-list {
  margin-bottom: 20rpx;
}

.case-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.case-user {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-avatar-small {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info-small {
  flex: 1;
}

.user-name-small {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 5rpx;
}

.publish-time {
  font-size: 24rpx;
  color: #999;
}

.case-tags {
  margin-bottom: 15rpx;
}

.tag {
  display: inline-block;
  background-color: #f0f8ff;
  color: #007AFF;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 10rpx;
  margin-bottom: 8rpx;
}

.case-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 15rpx;
}

.case-content {
  margin-bottom: 20rpx;
}

.content-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.content-text:not(.expanded) {
  -webkit-line-clamp: 3;
}

.expand-btn {
  color: #007AFF;
  font-size: 26rpx;
  margin-top: 10rpx;
  display: inline-block;
}

/* 案例图片 */
.case-images {
  margin-bottom: 20rpx;
}

.image-grid {
  display: flex;
  gap: 10rpx;
}

.image-item {
  position: relative;
  flex: 1;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.case-image {
  width: 100%;
  height: 100%;
}

.more-images {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-count {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
}

/* 案例操作 */
.case-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-item {
  display: flex;
  align-items: center;
}

.action-text {
  color: #007AFF;
  font-size: 26rpx;
}

.click-count {
  color: #999;
  font-size: 24rpx;
}



/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}
</style>
