<template>
  <view class="container">
    <view class="case-detail" v-if="caseInfo">
      <!-- 用户信息 -->
      <view class="user-section" @click="goToUserProfile">
        <image class="user-avatar" :src="processAvatarUrl(caseInfo.publisher.avatar)" mode="aspectFill"></image>
        <view class="user-info">
          <text class="user-name">{{ caseInfo.publisher.nickName }}</text>
          <text class="publish-time">{{ formatTime(caseInfo.createTime) }}</text>
        </view>
      </view>

      <!-- 案例标签 -->
      <view class="case-tags" v-if="caseInfo.caseTags">
        <text class="tag" v-for="tag in processTags(caseInfo.caseTags)" :key="tag">#{{ tag }}</text>
      </view>

      <!-- 案例标题 -->
      <view class="case-title">{{ caseInfo.caseTitle }}</view>

      <!-- 案例内容 -->
      <view class="case-content">
        <rich-text :nodes="caseInfo.caseContent"></rich-text>
      </view>

      <!-- 案例图片 -->
      <view class="case-images" v-if="caseInfo.caseImages">
        <view class="image-list">
          <view class="image-item" v-for="(image, index) in processImages(caseInfo.caseImages)" :key="index">
            <image class="case-image" :src="image" mode="widthFix"></image>
          </view>
        </view>
      </view>

      <!-- 点击统计 -->
      <view class="stats-section">
        <view class="stats-item">
          <text class="stats-label">{{ caseInfo.templateType }}</text>
          <text class="stats-label">阅读{{ caseInfo.clickCount }}次</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-section">
        <button class="action-btn poster-btn" @click="generatePoster">生成海报</button>
      </view>
    </view>

    <!-- 加载中 -->
    <view class="loading" v-else>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 海报弹窗 -->
    <view class="poster-modal" v-if="showPosterModal" @click="closePosterModal">
      <view class="poster-content" @click.stop>
        <view class="poster-header">
          <text class="poster-title">案例海报</text>
          <view class="poster-close" @click="closePosterModal">×</view>
        </view>

        <view class="poster-preview-container">
          <!-- 使用新版海报生成组件 - 1:1还原 -->
          <PosterGeneratorV2 ref="posterGenerator" :visible="showPosterModal" :caseId="caseId"
            :userInfo="caseInfo.publisher" :title="caseInfo.caseTitle" :content="caseInfo.caseContent"
            :tags="processTags(caseInfo.caseTags)" :images="processImages(caseInfo.caseImages)"
            :templateType="caseInfo.templateType" :clickCount="caseInfo.clickCount"
            :publishTime="formatTime(caseInfo.createTime)" :showImages="showPosterImages"
            :imageLimit="posterImageLimit" />
        </view>

        <view class="poster-controls">
          <button class="control-btn primary" @click="generateAndSavePoster">保存海报</button>
        </view>
      </view>
    </view>

    <!-- 隐藏的canvas用于生成海报 -->
    <canvas class="hidden-canvas" canvas-id="tempPosterCanvas" :style="{ width: '750px', height: '1334px' }"></canvas>

  </view>
</template>

<script>
import { caseInfoApi } from '@/utils/api.js'
import { formatTime, processImages, processAvatarUrl, processTags, stripHtml, generateQRData } from '@/utils/utils.js'
import PosterGeneratorV2 from '@/components/PosterGeneratorV2.vue'

export default {
  components: {
    PosterGeneratorV2
  },

  data() {
    return {
      caseId: null,
      caseInfo: null,
      showPosterModal: false,
      showPosterImages: true,
      posterImageLimit: 3,
      isGeneratingPoster: false
    }
  },

  onLoad(options) {
    this.caseId = options.caseId
    if (this.caseId) {
      this.loadCaseDetail()
    }
  },

  methods: {
    // 加载案例详情
    async loadCaseDetail() {
      try {
        const res = await caseInfoApi.getCaseDetail(this.caseId)
        this.caseInfo = res.data

        // 设置页面标题
        uni.setNavigationBarTitle({
          title: this.caseInfo.caseTitle
        })
      } catch (error) {
        console.error('加载案例详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    // 跳转到用户主页
    goToUserProfile() {
      uni.navigateTo({
        url: `/pages/user/profile?userId=${this.caseInfo.publisherId}`
      })
    },

    // 分享案例
    shareCase() {
      const shareData = {
        title: this.caseInfo.caseTitle,
        path: `/pages/case/detail?caseId=${this.caseId}`,
        imageUrl: this.processImages(this.caseInfo.caseImages)[0] || ''
      }

      // #ifdef MP-WEIXIN
      uni.showShareMenu({
        withShareTicket: true,
        menus: ['shareAppMessage', 'shareTimeline']
      })
      // #endif

      uni.showToast({
        title: '请使用右上角分享',
        icon: 'none'
      })
    },

    // 生成海报
    generatePoster() {
      this.showPosterModal = true
      this.$nextTick(() => {
        this.drawQRCode()
      })
    },

    // 绘制二维码
    drawQRCode() {
      const ctx = uni.createCanvasContext('qrCanvas', this)
      const size = 120

      // 清空画布
      ctx.clearRect(0, 0, size, size)

      // 设置背景色
      ctx.setFillStyle('#ffffff')
      ctx.fillRect(0, 0, size, size)

      // 绘制简单的二维码占位符
      ctx.setFillStyle('#000000')

      // 绘制二维码的简单模拟（实际项目中应该使用二维码生成库）
      const blockSize = 4
      for (let i = 0; i < size; i += blockSize) {
        for (let j = 0; j < size; j += blockSize) {
          if (Math.random() > 0.5) {
            ctx.fillRect(i, j, blockSize, blockSize)
          }
        }
      }

      // 绘制定位点
      const cornerSize = 20
      ctx.setFillStyle('#000000')
      // 左上角
      ctx.fillRect(0, 0, cornerSize, cornerSize)
      ctx.setFillStyle('#ffffff')
      ctx.fillRect(4, 4, cornerSize - 8, cornerSize - 8)
      ctx.setFillStyle('#000000')
      ctx.fillRect(8, 8, cornerSize - 16, cornerSize - 16)

      // 右上角
      ctx.setFillStyle('#000000')
      ctx.fillRect(size - cornerSize, 0, cornerSize, cornerSize)
      ctx.setFillStyle('#ffffff')
      ctx.fillRect(size - cornerSize + 4, 4, cornerSize - 8, cornerSize - 8)
      ctx.setFillStyle('#000000')
      ctx.fillRect(size - cornerSize + 8, 8, cornerSize - 16, cornerSize - 16)

      // 左下角
      ctx.setFillStyle('#000000')
      ctx.fillRect(0, size - cornerSize, cornerSize, cornerSize)
      ctx.setFillStyle('#ffffff')
      ctx.fillRect(4, size - cornerSize + 4, cornerSize - 8, cornerSize - 8)
      ctx.setFillStyle('#000000')
      ctx.fillRect(8, size - cornerSize + 8, cornerSize - 16, cornerSize - 16)

      ctx.draw()
    },

    // 切换海报图片显示
    togglePosterImages() {
      this.showPosterImages = !this.showPosterImages
    },

    // 切换图片数量限制
    toggleImageLimit() {
      const limits = [1, 2, 3, 6, 9]
      const currentIndex = limits.indexOf(this.posterImageLimit)
      this.posterImageLimit = limits[(currentIndex + 1) % limits.length]
    },

    // 生成并保存海报
    async generateAndSavePoster() {
      if (this.isGeneratingPoster) return

      this.isGeneratingPoster = true

      try {
        console.log('开始生成海报，案例信息:', this.caseInfo)

        // 确保海报组件已经渲染
        await this.$nextTick()

        // 使用海报生成组件生成海报
        const res = await this.$refs.posterGenerator.generatePoster()

        console.log('海报生成结果:', res)

        // 保存到相册
        await this.saveToAlbum(res.tempFilePath)

        uni.showToast({
          title: '海报保存成功',
          icon: 'success'
        })

        this.closePosterModal()

      } catch (error) {
        console.error('生成海报失败:', error)

        // 显示详细错误信息
        const errorMsg = error.message || '未知错误'
        uni.showModal({
          title: '生成海报失败',
          content: `错误信息: ${errorMsg}\n\n请检查网络连接和权限设置`,
          showCancel: false,
          confirmText: '确定'
        })
      } finally {
        this.isGeneratingPoster = false
      }
    },



    // 保存到相册
    async saveToAlbum(filePath) {
      try {
        // 检查相册权限
        await this.checkAlbumPermission()

        // 保存图片
        await new Promise((resolve, reject) => {
          uni.saveImageToPhotosAlbum({
            filePath: filePath,
            success: resolve,
            fail: reject
          })
        })

        console.log('图片保存成功:', filePath)

      } catch (error) {
        console.error('保存图片失败:', error)
        throw error
      }
    },

    // 检查相册权限
    checkAlbumPermission() {
      return new Promise((resolve, reject) => {
        // #ifdef MP-WEIXIN
        uni.getSetting({
          success: (res) => {
            if (res.authSetting['scope.writePhotosAlbum'] === false) {
              // 权限被拒绝，引导用户开启
              uni.showModal({
                title: '需要相册权限',
                content: '保存海报需要访问您的相册，请在设置中开启权限',
                confirmText: '去设置',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    uni.openSetting({
                      success: resolve,
                      fail: reject
                    })
                  } else {
                    reject(new Error('用户拒绝授权'))
                  }
                }
              })
            } else {
              resolve()
            }
          },
          fail: reject
        })
        // #endif

        // #ifndef MP-WEIXIN
        resolve()
        // #endif
      })
    },

    // 关闭海报弹窗
    closePosterModal() {
      this.showPosterModal = false
      this.showPosterImages = true
      this.posterImageLimit = 3
    },



    // 工具方法
    formatTime,
    processImages,
    processAvatarUrl,
    processTags
  }
}
</script>

<style scoped>
.container {
  padding-top: 5rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.case-detail {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}

/* 用户信息 */
.user-section {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.publish-time {
  font-size: 26rpx;
  color: #999;
}

/* 案例标签 */
.case-tags {
  margin-bottom: 20rpx;
}

.tag {
  display: inline-block;
  background-color: #f0f8ff;
  color: #007AFF;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  margin-right: 12rpx;
  margin-bottom: 10rpx;
}

/* 案例标题 */
.case-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 30rpx;
}

/* 案例内容 */
.case-content {
  font-size: 30rpx;
  color: #666;
  line-height: 1.8;
  margin-bottom: 30rpx;
}

/* 案例图片 */
.case-images {
  margin-bottom: 30rpx;
}

.image-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.image-item {
  width: 100%;
  border-radius: 12rpx;
  overflow: hidden;
}

.case-image {
  width: 100%;
  display: block;
}

/* 统计信息 */
.stats-section {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-label {
  font-size: 28rpx;
  color: #666;
}


/* 操作按钮 */
.action-section {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-btn {
  background-color: #f0f8ff;
  color: #007AFF;
}

.poster-btn {
  background-color: #007AFF;
  color: #fff;
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 海报弹窗 */
.poster-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.poster-content {
  background-color: #fff;
  border-radius: 16rpx;
  width: 90%;
  max-width: 700rpx;
  max-height: 85vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.poster-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.poster-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.poster-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.poster-preview-container {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx;
}

.poster-controls {
  display: flex;
  gap: 15rpx;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.control-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 35rpx;
  font-size: 26rpx;
  border: 1rpx solid #e0e0e0;
  background-color: #fff;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn.primary {
  background-color: #007AFF;
  color: #fff;
  border-color: #007AFF;
}

/* 隐藏的canvas */
.hidden-canvas {
  position: fixed;
  top: -9999px;
  left: -9999px;
  opacity: 0;
  pointer-events: none;
}
</style>
