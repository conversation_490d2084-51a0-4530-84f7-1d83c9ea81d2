<template>
  <view class="container">
    <view class="header">
      <text class="title">海报生成测试</text>
    </view>

    <view class="test-section">
      <button class="test-btn" @click="testPosterGeneration">测试海报生成</button>
      <button class="test-btn" @click="showPosterPreview">显示海报预览</button>
    </view>

    <!-- 海报预览 -->
    <view class="poster-container" v-if="showPreview">
      <PosterGenerator 
        ref="posterGenerator" 
        :visible="showPreview" 
        :caseId="testCaseData.caseId"
        :userInfo="testCaseData.publisher" 
        :title="testCaseData.caseTitle" 
        :content="testCaseData.caseContent"
        :tags="testCaseData.caseTags" 
        :images="testCaseData.caseImages"
        :templateType="testCaseData.templateType" 
        :clickCount="testCaseData.clickCount"
        :publishTime="testCaseData.publishTime" 
        :showImages="true"
        :imageLimit="3" 
      />
    </view>

    <!-- 生成的海报图片 -->
    <view class="result-section" v-if="generatedImagePath">
      <text class="result-title">生成的海报:</text>
      <image class="result-image" :src="generatedImagePath" mode="widthFix"></image>
      <button class="save-btn" @click="saveToAlbum">保存到相册</button>
    </view>
  </view>
</template>

<script>
import PosterGenerator from '@/components/PosterGenerator.vue'

export default {
  components: {
    PosterGenerator
  },

  data() {
    return {
      showPreview: false,
      generatedImagePath: '',
      testCaseData: {
        caseId: 'test-001',
        caseTitle: '测试案例标题 - 这是一个用于测试海报生成功能的案例',
        caseContent: '这是测试案例的内容。包含了<strong>HTML标签</strong>和<em>格式化文本</em>。内容比较长，用于测试文字换行和截断功能。这里还有更多的内容来测试海报生成的完整性。',
        caseTags: ['测试', '海报', 'uniapp', '案例管理'],
        caseImages: [
          'https://via.placeholder.com/400x300/007AFF/FFFFFF?text=Image1',
          'https://via.placeholder.com/400x300/FF6B6B/FFFFFF?text=Image2',
          'https://via.placeholder.com/400x300/4ECDC4/FFFFFF?text=Image3'
        ],
        templateType: '测试模板',
        clickCount: 123,
        publishTime: '2024-01-15 10:30',
        publisher: {
          nickName: '测试用户',
          avatar: 'https://via.placeholder.com/80x80/FFA500/FFFFFF?text=Avatar'
        }
      }
    }
  },

  methods: {
    // 显示海报预览
    showPosterPreview() {
      this.showPreview = true
    },

    // 测试海报生成
    async testPosterGeneration() {
      try {
        if (!this.showPreview) {
          this.showPreview = true
          await this.$nextTick()
        }

        console.log('开始测试海报生成...')
        const result = await this.$refs.posterGenerator.generatePoster()
        
        this.generatedImagePath = result.tempFilePath
        console.log('海报生成成功:', result)
        
        uni.showToast({
          title: '海报生成成功',
          icon: 'success'
        })

      } catch (error) {
        console.error('海报生成测试失败:', error)
        uni.showToast({
          title: '生成失败: ' + error.message,
          icon: 'none',
          duration: 3000
        })
      }
    },

    // 保存到相册
    async saveToAlbum() {
      try {
        await uni.saveImageToPhotosAlbum({
          filePath: this.generatedImagePath
        })
        
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })
      } catch (error) {
        console.error('保存失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.test-btn {
  flex: 1;
  height: 80rpx;
  background-color: #007AFF;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.poster-container {
  margin-bottom: 40rpx;
}

.result-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.result-image {
  width: 100%;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.save-btn {
  width: 100%;
  height: 80rpx;
  background-color: #4CAF50;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
