<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .controls button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007AFF;
            color: white;
        }
        .controls button:hover {
            background: #0056b3;
        }
        .controls input, .controls select {
            margin: 5px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .info {
            margin: 10px 0;
            padding: 10px;
            background: #e3f2fd;
            border-radius: 4px;
            font-family: monospace;
        }
        .case-list {
            margin-top: 20px;
        }
        .case-item {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            background: #fafafa;
        }
        .case-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .case-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }
        .case-content {
            color: #555;
            line-height: 1.4;
        }
        .error {
            color: red;
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>案例管理系统 API 测试</h1>
        
        <div class="controls">
            <label>API类型:</label>
            <select id="apiType">
                <option value="recommended">推荐案例</option>
                <option value="latest">最新案例</option>
            </select>
            
            <label>页码:</label>
            <input type="number" id="pageNum" value="1" min="1">
            
            <label>每页数量:</label>
            <input type="number" id="pageSize" value="10" min="1" max="50">
            
            <button onclick="loadData()">加载数据</button>
            <button onclick="loadMore()">加载更多</button>
            <button onclick="clearData()">清空数据</button>
        </div>

        <div class="info" id="info">
            点击"加载数据"开始测试
        </div>

        <div class="case-list" id="caseList">
            <!-- 案例列表将在这里显示 -->
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:7788';
        let allCases = [];
        let currentPage = 1;

        function updateInfo(message) {
            document.getElementById('info').innerHTML = message;
        }

        function showError(message) {
            document.getElementById('info').innerHTML = `<div class="error">${message}</div>`;
        }

        function showLoading() {
            document.getElementById('info').innerHTML = '<div class="loading">加载中...</div>';
        }

        async function makeRequest(url) {
            try {
                showLoading();
                console.log('请求URL:', url);
                
                const response = await fetch(url);
                const data = await response.json();
                
                console.log('响应数据:', data);
                
                if (data.code === 200) {
                    return data.data || [];
                } else {
                    throw new Error(data.msg || '请求失败');
                }
            } catch (error) {
                console.error('请求错误:', error);
                showError(`请求失败: ${error.message}`);
                return null;
            }
        }

        async function loadData() {
            const apiType = document.getElementById('apiType').value;
            const pageNum = parseInt(document.getElementById('pageNum').value);
            const pageSize = parseInt(document.getElementById('pageSize').value);
            
            currentPage = pageNum;
            
            const url = `${BASE_URL}/system/caseInfo/mobile/${apiType}?pageNum=${pageNum}&pageSize=${pageSize}`;
            const cases = await makeRequest(url);
            
            if (cases !== null) {
                allCases = cases;
                renderCases();
                updateInfo(`成功加载 ${cases.length} 条数据 (第${pageNum}页，每页${pageSize}条)`);
            }
        }

        async function loadMore() {
            const apiType = document.getElementById('apiType').value;
            const pageSize = parseInt(document.getElementById('pageSize').value);
            
            currentPage++;
            document.getElementById('pageNum').value = currentPage;
            
            const url = `${BASE_URL}/system/caseInfo/mobile/${apiType}?pageNum=${currentPage}&pageSize=${pageSize}`;
            const cases = await makeRequest(url);
            
            if (cases !== null) {
                if (cases.length === 0) {
                    updateInfo(`没有更多数据了 (总共${allCases.length}条)`);
                    currentPage--; // 回退页码
                    document.getElementById('pageNum').value = currentPage;
                } else {
                    allCases = [...allCases, ...cases];
                    renderCases();
                    updateInfo(`加载更多成功，新增 ${cases.length} 条数据 (第${currentPage}页，总共${allCases.length}条)`);
                }
            } else {
                currentPage--; // 请求失败时回退页码
                document.getElementById('pageNum').value = currentPage;
            }
        }

        function clearData() {
            allCases = [];
            currentPage = 1;
            document.getElementById('pageNum').value = 1;
            document.getElementById('caseList').innerHTML = '';
            updateInfo('数据已清空');
        }

        function renderCases() {
            const caseListEl = document.getElementById('caseList');
            
            if (allCases.length === 0) {
                caseListEl.innerHTML = '<div class="case-item">暂无数据</div>';
                return;
            }
            
            const html = allCases.map((caseItem, index) => `
                <div class="case-item">
                    <div class="case-title">${index + 1}. ${caseItem.caseTitle || '无标题'}</div>
                    <div class="case-meta">
                        ID: ${caseItem.caseId} | 
                        发布者: ${caseItem.publisherId} | 
                        点击量: ${caseItem.clickCount || 0} |
                        创建时间: ${caseItem.createTime || '未知'}
                    </div>
                    <div class="case-content">
                        ${caseItem.caseContent ? caseItem.caseContent.substring(0, 200) + '...' : '无内容'}
                    </div>
                </div>
            `).join('');
            
            caseListEl.innerHTML = html;
        }

        // 页面加载完成后自动加载第一页数据
        window.onload = function() {
            loadData();
        };
    </script>
</body>
</html>
