// 海报1:1还原验证工具
export class PosterValidator {
  constructor() {
    this.tolerance = 5 // 允许的像素误差
  }

  // 验证海报生成的1:1还原效果
  async validatePosterAccuracy(component, caseId) {
    try {
      console.log('开始验证海报1:1还原效果...')
      
      // 1. 获取DOM元素信息
      const domInfo = await this.getDOMInfo(component, caseId)
      console.log('DOM信息获取完成:', domInfo)
      
      // 2. 生成海报
      const posterResult = await component.generatePoster()
      console.log('海报生成完成:', posterResult)
      
      // 3. 验证关键指标
      const validationResult = this.validateKeyMetrics(domInfo, posterResult)
      console.log('验证结果:', validationResult)
      
      return validationResult
      
    } catch (error) {
      console.error('验证过程出错:', error)
      return {
        success: false,
        error: error.message,
        details: []
      }
    }
  }

  // 获取DOM信息
  getDOMInfo(component, caseId) {
    return new Promise((resolve, reject) => {
      const query = uni.createSelectorQuery().in(component)
      
      // 查询所有关键元素
      const selectors = [
        `#poster-preview-${caseId}`,
        `#user-section-${caseId}`,
        `#user-avatar-${caseId}`,
        `#user-name-${caseId}`,
        `#publish-time-${caseId}`,
        `#case-tags-${caseId}`,
        `#case-title-${caseId}`,
        `#case-content-${caseId}`,
        `#stats-section-${caseId}`,
        `#qr-section-${caseId}`,
        `#brand-section-${caseId}`
      ]
      
      selectors.forEach(selector => {
        query.select(selector).boundingClientRect()
      })
      
      query.exec((res) => {
        if (res && res[0]) {
          const domInfo = {
            container: res[0],
            userSection: res[1],
            userAvatar: res[2],
            userName: res[3],
            publishTime: res[4],
            caseTags: res[5],
            caseTitle: res[6],
            caseContent: res[7],
            statsSection: res[8],
            qrSection: res[9],
            brandSection: res[10],
            timestamp: Date.now()
          }
          resolve(domInfo)
        } else {
          reject(new Error('无法获取DOM信息'))
        }
      })
    })
  }

  // 验证关键指标
  validateKeyMetrics(domInfo, posterResult) {
    const validationResults = []
    
    // 1. 验证容器尺寸
    if (domInfo.container) {
      validationResults.push({
        metric: '容器尺寸',
        status: 'info',
        message: `DOM尺寸: ${domInfo.container.width}x${domInfo.container.height}`,
        expected: domInfo.container,
        actual: posterResult
      })
    }
    
    // 2. 验证用户头像位置
    if (domInfo.userAvatar) {
      const avatarValidation = this.validateElementPosition(
        domInfo.userAvatar, 
        domInfo.container,
        '用户头像'
      )
      validationResults.push(avatarValidation)
    }
    
    // 3. 验证用户名位置
    if (domInfo.userName) {
      const nameValidation = this.validateElementPosition(
        domInfo.userName,
        domInfo.container,
        '用户名'
      )
      validationResults.push(nameValidation)
    }
    
    // 4. 验证标题位置
    if (domInfo.caseTitle) {
      const titleValidation = this.validateElementPosition(
        domInfo.caseTitle,
        domInfo.container,
        '案例标题'
      )
      validationResults.push(titleValidation)
    }
    
    // 5. 验证内容区域
    if (domInfo.caseContent) {
      const contentValidation = this.validateElementPosition(
        domInfo.caseContent,
        domInfo.container,
        '案例内容'
      )
      validationResults.push(contentValidation)
    }
    
    // 6. 验证统计信息
    if (domInfo.statsSection) {
      const statsValidation = this.validateElementPosition(
        domInfo.statsSection,
        domInfo.container,
        '统计信息'
      )
      validationResults.push(statsValidation)
    }
    
    // 计算总体得分
    const successCount = validationResults.filter(r => r.status === 'success').length
    const totalCount = validationResults.length
    const accuracy = totalCount > 0 ? (successCount / totalCount * 100).toFixed(1) : 0
    
    return {
      success: accuracy >= 80, // 80%以上认为成功
      accuracy: accuracy + '%',
      details: validationResults,
      summary: {
        total: totalCount,
        success: successCount,
        warning: validationResults.filter(r => r.status === 'warning').length,
        error: validationResults.filter(r => r.status === 'error').length
      }
    }
  }

  // 验证元素位置
  validateElementPosition(element, container, elementName) {
    if (!element || !container) {
      return {
        metric: elementName,
        status: 'error',
        message: '元素信息缺失',
        expected: null,
        actual: null
      }
    }
    
    // 计算相对位置
    const relativeX = element.left - container.left
    const relativeY = element.top - container.top
    
    // 计算相对尺寸比例
    const widthRatio = (element.width / container.width * 100).toFixed(1)
    const heightRatio = (element.height / container.height * 100).toFixed(1)
    
    return {
      metric: elementName,
      status: 'info', // 实际验证需要与Canvas结果对比
      message: `位置: (${relativeX.toFixed(1)}, ${relativeY.toFixed(1)}), 尺寸比例: ${widthRatio}% x ${heightRatio}%`,
      expected: {
        x: relativeX,
        y: relativeY,
        width: element.width,
        height: element.height,
        widthRatio: parseFloat(widthRatio),
        heightRatio: parseFloat(heightRatio)
      },
      actual: null // 需要Canvas绘制结果来填充
    }
  }

  // 生成验证报告
  generateReport(validationResult) {
    const report = {
      title: '海报1:1还原验证报告',
      timestamp: new Date().toLocaleString(),
      summary: validationResult.summary,
      accuracy: validationResult.accuracy,
      success: validationResult.success,
      details: validationResult.details
    }
    
    console.log('=== 海报验证报告 ===')
    console.log(`验证时间: ${report.timestamp}`)
    console.log(`还原精度: ${report.accuracy}`)
    console.log(`验证状态: ${report.success ? '通过' : '未通过'}`)
    console.log(`总计项目: ${report.summary.total}`)
    console.log(`成功项目: ${report.summary.success}`)
    console.log(`警告项目: ${report.summary.warning}`)
    console.log(`错误项目: ${report.summary.error}`)
    console.log('详细信息:')
    
    report.details.forEach((detail, index) => {
      console.log(`${index + 1}. ${detail.metric}: ${detail.message} [${detail.status}]`)
    })
    
    return report
  }

  // 快速验证方法
  async quickValidate(component, caseId) {
    try {
      const result = await this.validatePosterAccuracy(component, caseId)
      const report = this.generateReport(result)
      
      // 显示用户友好的提示
      if (result.success) {
        uni.showToast({
          title: `验证通过 (${result.accuracy})`,
          icon: 'success',
          duration: 2000
        })
      } else {
        uni.showModal({
          title: '验证结果',
          content: `还原精度: ${result.accuracy}\n建议检查样式配置和元素位置`,
          showCancel: false
        })
      }
      
      return report
      
    } catch (error) {
      console.error('快速验证失败:', error)
      uni.showToast({
        title: '验证失败',
        icon: 'none'
      })
      return null
    }
  }
}

// 导出单例
export const posterValidator = new PosterValidator()

// 使用示例:
// import { posterValidator } from '@/utils/posterValidator.js'
// 
// // 在组件中使用
// async validatePoster() {
//   const report = await posterValidator.quickValidate(this.$refs.posterGenerator, this.caseId)
//   console.log('验证报告:', report)
// }
