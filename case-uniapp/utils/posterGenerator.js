// 海报生成工具类
import { stripHtml, generateQRData } from "./utils.js";

export class PosterGenerator {
  constructor(options = {}) {
    this.canvasWidth = options.width || 750;
    this.canvasHeight = options.height || 1334;
    this.padding = options.padding || 40;
    this.backgroundColor = options.backgroundColor || "#ffffff";
    this.primaryColor = options.primaryColor || "#333333";
    this.secondaryColor = options.secondaryColor || "#666666";
    this.accentColor = options.accentColor || "#007AFF";
  }

  // 生成案例海报
  async generateCasePoster(caseInfo, options = {}) {
    const {
      showImages = true,
      imageLimit = 3,
      canvasId = "posterCanvas",
      context = null,
    } = options;

    if (!context) {
      throw new Error("需要提供canvas上下文");
    }

    try {
      console.log("开始生成海报，案例信息:", caseInfo);

      const ctx = uni.createCanvasContext(canvasId, context);

      // 动态计算画布高度
      const estimatedHeight = this.estimateCanvasHeight(
        caseInfo,
        showImages,
        imageLimit
      );
      this.canvasHeight = Math.max(this.canvasHeight, estimatedHeight);

      console.log("预估画布高度:", estimatedHeight);

      // 清空画布并设置背景
      this.setupCanvas(ctx);

      let currentY = this.padding;

      // 绘制用户信息
      currentY = await this.drawUserSectionComplete(
        ctx,
        caseInfo.publisher,
        currentY
      );

      // 绘制标签
      if (caseInfo.caseTags && caseInfo.caseTags.length > 0) {
        currentY = this.drawTagsComplete(ctx, caseInfo.caseTags, currentY);
      }

      // 绘制标题
      currentY = this.drawTitleComplete(ctx, caseInfo.caseTitle, currentY);

      // 绘制内容
      currentY = this.drawContentComplete(ctx, caseInfo.caseContent, currentY);

      // 绘制图片（如果启用）
      if (showImages && caseInfo.caseImages) {
        currentY = await this.drawImagesComplete(
          ctx,
          caseInfo.caseImages,
          currentY,
          imageLimit
        );
      }

      // 绘制统计信息
      currentY = this.drawStatsComplete(ctx, caseInfo, currentY);

      // 绘制二维码和底部信息
      this.drawFooterComplete(ctx, caseInfo.caseId, currentY);

      // 执行绘制
      return new Promise((resolve, reject) => {
        ctx.draw(false, () => {
          setTimeout(() => {
            uni.canvasToTempFilePath(
              {
                canvasId: canvasId,
                width: this.canvasWidth,
                height: this.canvasHeight,
                success: (res) => {
                  console.log("海报生成成功:", res);
                  resolve(res);
                },
                fail: (error) => {
                  console.error("Canvas转图片失败:", error);
                  reject(error);
                },
              },
              context
            );
          }, 1000);
        });
      });
    } catch (error) {
      console.error("生成海报过程中出错:", error);
      throw error;
    }
  }

  // 估算画布高度
  estimateCanvasHeight(caseInfo, showImages, imageLimit) {
    let estimatedHeight = this.padding * 2; // 上下边距

    // 用户信息区域
    estimatedHeight += 100;

    // 标签区域
    if (caseInfo.caseTags && caseInfo.caseTags.length > 0) {
      const tagRows = Math.ceil(caseInfo.caseTags.length / 4); // 假设每行4个标签
      estimatedHeight += tagRows * 50;
    }

    // 标题区域（估算行数）
    const titleLength = caseInfo.caseTitle ? caseInfo.caseTitle.length : 0;
    const titleLines = Math.ceil(titleLength / 20); // 假设每行20个字符
    estimatedHeight += titleLines * 60;

    // 内容区域
    const contentLength = caseInfo.caseContent
      ? caseInfo.caseContent.replace(/<[^>]*>/g, "").length
      : 0;
    const contentLines = Math.min(Math.ceil(contentLength / 25), 20); // 最多20行
    estimatedHeight += contentLines * 50;

    // 图片区域
    if (showImages && caseInfo.caseImages) {
      const imageCount = Math.min(
        caseInfo.caseImages.split(",").length,
        imageLimit
      );
      const imageRows = Math.ceil(imageCount / 3); // 每行3张图片
      estimatedHeight += imageRows * 200 + 50;
    }

    // 统计信息和底部区域
    estimatedHeight += 200;

    return Math.max(estimatedHeight, 1334); // 最小高度
  }

  // 设置画布
  setupCanvas(ctx) {
    ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
    ctx.setFillStyle(this.backgroundColor);
    ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
  }

  // 绘制用户信息
  async drawUserSection(ctx, publisher, startY) {
    const avatarSize = 80;
    const spacing = 20;

    // 绘制头像背景圆
    ctx.setFillStyle("#f0f0f0");
    ctx.beginPath();
    ctx.arc(
      this.padding + avatarSize / 2,
      startY + avatarSize / 2,
      avatarSize / 2,
      0,
      2 * Math.PI
    );
    ctx.fill();

    // 绘制用户名
    ctx.setFillStyle(this.primaryColor);
    ctx.setFontSize(28);
    ctx.setTextAlign("left");
    ctx.fillText(
      publisher.nickName,
      this.padding + avatarSize + spacing,
      startY + 30
    );

    // 绘制发布时间
    ctx.setFillStyle(this.secondaryColor);
    ctx.setFontSize(22);
    ctx.fillText("刚刚发布", this.padding + avatarSize + spacing, startY + 60);

    return startY + avatarSize + 30;
  }

  // 绘制标签
  drawTags(ctx, tagsString, startY) {
    const tags = tagsString.split(",").filter((tag) => tag.trim());
    if (tags.length === 0) return startY;

    let currentX = this.padding;
    let currentRowY = startY;
    const tagHeight = 40;
    const tagSpacing = 15;
    const rowSpacing = 15;

    ctx.setFillStyle(this.accentColor);
    ctx.setFontSize(20);
    ctx.setTextAlign("left");

    tags.forEach((tag) => {
      const tagText = `#${tag.trim()}`;
      const textWidth = ctx.measureText(tagText).width;
      const tagWidth = textWidth + 30;

      // 检查是否需要换行
      if (currentX + tagWidth > this.canvasWidth - this.padding) {
        currentX = this.padding;
        currentRowY += tagHeight + rowSpacing;
      }

      // 绘制标签背景
      ctx.setFillStyle("#e3f2fd");
      this.drawRoundedRect(ctx, currentX, currentRowY, tagWidth, tagHeight, 20);
      ctx.fill();

      // 绘制标签文字
      ctx.setFillStyle(this.accentColor);
      ctx.fillText(tagText, currentX + 15, currentRowY + 28);

      currentX += tagWidth + tagSpacing;
    });

    return currentRowY + tagHeight + 30;
  }

  // 绘制标题
  drawTitle(ctx, title, startY) {
    ctx.setFillStyle(this.primaryColor);
    ctx.setFontSize(36);
    ctx.setTextAlign("left");

    const lines = this.wrapText(
      ctx,
      title,
      this.canvasWidth - this.padding * 2,
      36
    );
    const lineHeight = 50;

    lines.forEach((line, index) => {
      ctx.fillText(line, this.padding, startY + (index + 1) * lineHeight);
    });

    return startY + lines.length * lineHeight + 30;
  }

  // 绘制内容
  drawContent(ctx, content, startY) {
    const plainText = stripHtml(content);
    const maxLength = 500; // 限制内容长度
    const displayText =
      plainText.length > maxLength
        ? plainText.substring(0, maxLength) + "..."
        : plainText;

    ctx.setFillStyle(this.primaryColor);
    ctx.setFontSize(28);
    ctx.setTextAlign("left");

    const lines = this.wrapText(
      ctx,
      displayText,
      this.canvasWidth - this.padding * 2,
      28
    );
    const lineHeight = 40;
    const maxLines = 15; // 限制行数

    const displayLines = lines.slice(0, maxLines);

    displayLines.forEach((line, index) => {
      ctx.fillText(line, this.padding, startY + (index + 1) * lineHeight);
    });

    // 如果内容被截断，添加省略号提示
    if (lines.length > maxLines || plainText.length > maxLength) {
      ctx.setFillStyle(this.secondaryColor);
      ctx.setFontSize(24);
      ctx.fillText(
        "...查看完整内容请扫描下方二维码",
        this.padding,
        startY + (displayLines.length + 1) * lineHeight + 20
      );
    }

    return startY + displayLines.length * lineHeight + 50;
  }

  // 绘制图片占位符
  async drawImages(ctx, imagesString, startY, limit) {
    const images = imagesString
      .split(",")
      .filter((img) => img.trim())
      .slice(0, limit);
    if (images.length === 0) return startY;

    const imageSize =
      (this.canvasWidth -
        this.padding * 2 -
        20 * (Math.min(images.length, 3) - 1)) /
      Math.min(images.length, 3);
    const imageHeight = imageSize * 0.75; // 4:3 比例

    let currentX = this.padding;
    let currentY = startY;

    images.forEach((image, index) => {
      if (index > 0 && index % 3 === 0) {
        currentX = this.padding;
        currentY += imageHeight + 20;
      }

      // 绘制图片占位符
      ctx.setFillStyle("#f5f5f5");
      ctx.fillRect(currentX, currentY, imageSize, imageHeight);

      // 绘制图片边框
      ctx.setStrokeStyle("#e0e0e0");
      ctx.setLineWidth(2);
      ctx.strokeRect(currentX, currentY, imageSize, imageHeight);

      // 绘制图片图标
      ctx.setFillStyle("#cccccc");
      ctx.setFontSize(24);
      ctx.setTextAlign("center");
      ctx.fillText(
        "图片",
        currentX + imageSize / 2,
        currentY + imageHeight / 2
      );

      currentX += imageSize + 20;
    });

    return currentY + imageHeight + 30;
  }

  // 绘制统计信息
  drawStats(ctx, caseInfo, startY) {
    // 绘制分割线
    ctx.setStrokeStyle("#f0f0f0");
    ctx.setLineWidth(2);
    ctx.beginPath();
    ctx.moveTo(this.padding, startY);
    ctx.lineTo(this.canvasWidth - this.padding, startY);
    ctx.stroke();

    const statsY = startY + 30;

    ctx.setFillStyle(this.secondaryColor);
    ctx.setFontSize(24);
    ctx.setTextAlign("left");
    ctx.fillText(caseInfo.templateType, this.padding, statsY);

    ctx.setTextAlign("right");
    ctx.fillText(
      `阅读 ${caseInfo.clickCount} 次`,
      this.canvasWidth - this.padding,
      statsY
    );

    return statsY + 40;
  }

  // 绘制二维码
  drawQRCode(ctx, caseId) {
    const qrSize = 120;
    const qrX = this.canvasWidth - this.padding - qrSize;
    const qrY = this.canvasHeight - this.padding - qrSize - 60;

    // 绘制二维码背景
    ctx.setFillStyle("#ffffff");
    ctx.fillRect(qrX, qrY, qrSize, qrSize);

    // 绘制二维码边框
    ctx.setStrokeStyle("#e0e0e0");
    ctx.setLineWidth(2);
    ctx.strokeRect(qrX, qrY, qrSize, qrSize);

    // 绘制简单的二维码图案
    ctx.setFillStyle("#000000");
    const blockSize = 4;
    for (let i = 0; i < qrSize; i += blockSize) {
      for (let j = 0; j < qrSize; j += blockSize) {
        if (Math.random() > 0.5) {
          ctx.fillRect(qrX + i, qrY + j, blockSize, blockSize);
        }
      }
    }

    // 绘制定位点
    this.drawQRCorner(ctx, qrX, qrY, 20);
    this.drawQRCorner(ctx, qrX + qrSize - 20, qrY, 20);
    this.drawQRCorner(ctx, qrX, qrY + qrSize - 20, 20);

    // 绘制二维码说明文字
    ctx.setFillStyle(this.secondaryColor);
    ctx.setFontSize(20);
    ctx.setTextAlign("center");
    ctx.fillText("扫码查看完整内容", qrX + qrSize / 2, qrY + qrSize + 30);
  }

  // 绘制二维码定位点
  drawQRCorner(ctx, x, y, size) {
    ctx.setFillStyle("#000000");
    ctx.fillRect(x, y, size, size);
    ctx.setFillStyle("#ffffff");
    ctx.fillRect(x + 4, y + 4, size - 8, size - 8);
    ctx.setFillStyle("#000000");
    ctx.fillRect(x + 8, y + 8, size - 16, size - 16);
  }

  // 绘制圆角矩形
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
  }

  // 文字换行处理
  wrapText(ctx, text, maxWidth, fontSize) {
    const words = text.split("");
    const lines = [];
    let currentLine = "";

    for (let i = 0; i < words.length; i++) {
      const testLine = currentLine + words[i];
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;

      if (testWidth > maxWidth && i > 0) {
        lines.push(currentLine);
        currentLine = words[i];
      } else {
        currentLine = testLine;
      }
    }
    lines.push(currentLine);
    return lines;
  }
}

// 导出默认实例
export const posterGenerator = new PosterGenerator();
