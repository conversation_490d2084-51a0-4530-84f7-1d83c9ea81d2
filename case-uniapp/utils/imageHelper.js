// 图片处理辅助工具
export class ImageHelper {
  constructor() {
    this.cache = new Map()
    this.downloadQueue = new Map() // 防止重复下载
  }

  // 处理头像URL，确保可用性
  processAvatarUrl(avatarUrl) {
    if (!avatarUrl || avatarUrl.trim() === '') {
      return null
    }

    // 处理相对路径
    if (avatarUrl.startsWith('/')) {
      // 如果是相对路径，需要根据实际情况添加域名
      return avatarUrl
    }

    // 处理网络图片
    if (avatarUrl.startsWith('http')) {
      return avatarUrl
    }

    // 处理本地图片
    return avatarUrl
  }

  // 下载并缓存图片
  async downloadImage(imageUrl) {
    if (!imageUrl) {
      throw new Error('图片URL为空')
    }

    // 处理URL
    const processedUrl = this.processAvatarUrl(imageUrl)
    if (!processedUrl) {
      throw new Error('无效的图片URL')
    }

    // 检查缓存
    if (this.cache.has(processedUrl)) {
      console.log('使用缓存图片:', processedUrl)
      return this.cache.get(processedUrl)
    }

    // 检查是否正在下载
    if (this.downloadQueue.has(processedUrl)) {
      console.log('等待图片下载完成:', processedUrl)
      return await this.downloadQueue.get(processedUrl)
    }

    // 如果是本地图片，直接返回
    if (!processedUrl.startsWith('http')) {
      this.cache.set(processedUrl, processedUrl)
      return processedUrl
    }

    // 创建下载Promise
    const downloadPromise = new Promise((resolve, reject) => {
      console.log('开始下载图片:', processedUrl)
      
      uni.downloadFile({
        url: processedUrl,
        timeout: 10000, // 10秒超时
        success: (res) => {
          if (res.statusCode === 200) {
            console.log('图片下载成功:', res.tempFilePath)
            this.cache.set(processedUrl, res.tempFilePath)
            resolve(res.tempFilePath)
          } else {
            const error = new Error(`图片下载失败，状态码: ${res.statusCode}`)
            console.error(error.message)
            reject(error)
          }
        },
        fail: (error) => {
          console.error('图片下载失败:', error)
          reject(new Error(`图片下载失败: ${error.errMsg || '未知错误'}`))
        },
        complete: () => {
          // 下载完成后从队列中移除
          this.downloadQueue.delete(processedUrl)
        }
      })
    })

    // 添加到下载队列
    this.downloadQueue.set(processedUrl, downloadPromise)

    return await downloadPromise
  }

  // 批量下载图片
  async downloadImages(imageUrls) {
    const results = []
    
    for (const url of imageUrls) {
      try {
        const path = await this.downloadImage(url)
        results.push({ url, path, success: true })
      } catch (error) {
        console.error(`下载图片失败 ${url}:`, error)
        results.push({ url, path: null, success: false, error: error.message })
      }
    }
    
    return results
  }

  // 预加载图片
  async preloadImages(imageUrls) {
    console.log('开始预加载图片:', imageUrls.length)
    
    const promises = imageUrls.map(url => 
      this.downloadImage(url).catch(error => {
        console.warn(`预加载图片失败 ${url}:`, error)
        return null
      })
    )
    
    const results = await Promise.all(promises)
    const successCount = results.filter(r => r !== null).length
    
    console.log(`预加载完成: ${successCount}/${imageUrls.length}`)
    return results
  }

  // 清理缓存
  clearCache() {
    console.log('清理图片缓存')
    this.cache.clear()
    this.downloadQueue.clear()
  }

  // 获取缓存信息
  getCacheInfo() {
    return {
      cacheSize: this.cache.size,
      downloadingCount: this.downloadQueue.size,
      cachedUrls: Array.from(this.cache.keys())
    }
  }

  // 验证图片是否可用
  async validateImage(imageUrl) {
    try {
      const path = await this.downloadImage(imageUrl)
      return { valid: true, path }
    } catch (error) {
      return { valid: false, error: error.message }
    }
  }

  // 生成默认头像
  generateDefaultAvatar(name = '用户', size = 80) {
    // 这里可以实现生成默认头像的逻辑
    // 比如根据用户名生成颜色和首字母
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
    const colorIndex = name.length % colors.length
    const color = colors[colorIndex]
    const firstChar = name.charAt(0).toUpperCase()
    
    // 返回一个可以用于Canvas的默认头像配置
    return {
      type: 'text',
      text: firstChar,
      backgroundColor: color,
      textColor: '#FFFFFF',
      size: size
    }
  }
}

// 创建单例
export const imageHelper = new ImageHelper()

// 便捷方法
export const downloadImage = (url) => imageHelper.downloadImage(url)
export const preloadImages = (urls) => imageHelper.preloadImages(urls)
export const processAvatarUrl = (url) => imageHelper.processAvatarUrl(url)

// 使用示例:
// import { imageHelper, downloadImage } from '@/utils/imageHelper.js'
// 
// // 下载单张图片
// const imagePath = await downloadImage('https://example.com/avatar.jpg')
// 
// // 批量下载
// const results = await imageHelper.downloadImages([url1, url2, url3])
// 
// // 预加载
// await imageHelper.preloadImages([url1, url2, url3])
// 
// // 清理缓存
// imageHelper.clearCache()
