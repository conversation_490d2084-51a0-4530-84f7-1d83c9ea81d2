// 图片相关配置
import { BASE_URL } from './api.js'

// 默认图片配置
export const IMAGE_CONFIG = {
  // 默认头像
  DEFAULT_AVATAR: '/static/default-avatar.svg',
  
  // 默认案例图片
  DEFAULT_CASE_IMAGE: '/static/default-case.svg',
  
  // 图片路径前缀
  AVATAR_PATH: '/profile',
  CASE_IMAGE_PATH: '/uploads/cases',
  
  // 图片质量配置
  QUALITY: {
    THUMBNAIL: 'thumbnail', // 缩略图
    MEDIUM: 'medium',       // 中等质量
    HIGH: 'high'           // 高质量
  },
  
  // 图片尺寸配置
  SIZES: {
    AVATAR_SMALL: '60x60',
    AVATAR_MEDIUM: '120x120', 
    AVATAR_LARGE: '200x200',
    CASE_THUMBNAIL: '300x200',
    CASE_MEDIUM: '600x400',
    CASE_LARGE: '1200x800'
  }
}

// 构建完整图片URL
export const buildImageUrl = (imagePath, options = {}) => {
  if (!imagePath) return IMAGE_CONFIG.DEFAULT_AVATAR
  
  // 如果已经是完整URL，直接返回
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath
  }
  
  // 如果是本地静态资源，直接返回
  if (imagePath.startsWith('/static/')) {
    return imagePath
  }
  
  let url = BASE_URL
  
  // 添加路径前缀
  if (imagePath.startsWith('/')) {
    url += imagePath
  } else {
    // 根据图片类型添加不同的路径前缀
    const pathPrefix = options.type === 'case' ? IMAGE_CONFIG.CASE_IMAGE_PATH : IMAGE_CONFIG.AVATAR_PATH
    url += `${pathPrefix}/${imagePath}`
  }
  
  // 添加质量和尺寸参数
  const params = []
  if (options.quality) {
    params.push(`quality=${options.quality}`)
  }
  if (options.size) {
    params.push(`size=${options.size}`)
  }
  
  if (params.length > 0) {
    url += `?${params.join('&')}`
  }
  
  return url
}

// 获取头像URL
export const getAvatarUrl = (avatarPath, size = 'medium') => {
  return buildImageUrl(avatarPath, {
    type: 'avatar',
    size: IMAGE_CONFIG.SIZES[`AVATAR_${size.toUpperCase()}`] || size
  })
}

// 获取案例图片URL
export const getCaseImageUrl = (imagePath, size = 'medium') => {
  return buildImageUrl(imagePath, {
    type: 'case',
    size: IMAGE_CONFIG.SIZES[`CASE_${size.toUpperCase()}`] || size
  })
}

// 图片错误处理
export const handleImageError = (imageSrc, fallbackSrc) => {
  console.warn(`图片加载失败: ${imageSrc}，使用备用图片: ${fallbackSrc}`)
  return fallbackSrc || IMAGE_CONFIG.DEFAULT_AVATAR
}
