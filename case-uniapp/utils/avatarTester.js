// 头像功能测试工具
import { imageHelper } from './imageHelper.js'

export class AvatarTester {
  constructor() {
    this.testResults = []
  }

  // 测试不同类型的头像URL
  async testAvatarUrls() {
    console.log('开始测试头像URL处理...')
    
    const testCases = [
      {
        name: '正常网络图片',
        url: 'https://picsum.photos/80/80?random=1',
        expected: 'success'
      },
      {
        name: '另一个网络图片',
        url: 'https://picsum.photos/80/80?random=2',
        expected: 'success'
      },
      {
        name: '无效URL',
        url: 'https://invalid-domain-12345.com/avatar.jpg',
        expected: 'fail'
      },
      {
        name: '空字符串',
        url: '',
        expected: 'fail'
      },
      {
        name: 'null值',
        url: null,
        expected: 'fail'
      },
      {
        name: '本地图片路径',
        url: '/static/images/default-avatar.png',
        expected: 'success'
      }
    ]

    for (const testCase of testCases) {
      await this.testSingleUrl(testCase)
    }

    this.printResults()
    return this.testResults
  }

  // 测试单个URL
  async testSingleUrl(testCase) {
    console.log(`测试: ${testCase.name} - ${testCase.url}`)
    
    const startTime = Date.now()
    
    try {
      const result = await imageHelper.validateImage(testCase.url)
      const endTime = Date.now()
      const duration = endTime - startTime
      
      const testResult = {
        name: testCase.name,
        url: testCase.url,
        success: result.valid,
        path: result.path,
        error: result.error,
        duration: duration,
        expected: testCase.expected,
        passed: (result.valid && testCase.expected === 'success') || 
                (!result.valid && testCase.expected === 'fail')
      }
      
      this.testResults.push(testResult)
      
      if (testResult.passed) {
        console.log(`✅ ${testCase.name}: 通过 (${duration}ms)`)
      } else {
        console.log(`❌ ${testCase.name}: 失败 (${duration}ms)`)
      }
      
    } catch (error) {
      const endTime = Date.now()
      const duration = endTime - startTime
      
      const testResult = {
        name: testCase.name,
        url: testCase.url,
        success: false,
        path: null,
        error: error.message,
        duration: duration,
        expected: testCase.expected,
        passed: testCase.expected === 'fail'
      }
      
      this.testResults.push(testResult)
      
      if (testResult.passed) {
        console.log(`✅ ${testCase.name}: 通过 (预期失败, ${duration}ms)`)
      } else {
        console.log(`❌ ${testCase.name}: 失败 (${error.message}, ${duration}ms)`)
      }
    }
  }

  // 测试缓存功能
  async testCaching() {
    console.log('开始测试缓存功能...')
    
    const testUrl = 'https://picsum.photos/80/80?random=cache-test'
    
    // 第一次下载
    console.log('第一次下载...')
    const start1 = Date.now()
    await imageHelper.downloadImage(testUrl)
    const duration1 = Date.now() - start1
    
    // 第二次下载（应该使用缓存）
    console.log('第二次下载（使用缓存）...')
    const start2 = Date.now()
    await imageHelper.downloadImage(testUrl)
    const duration2 = Date.now() - start2
    
    console.log(`第一次下载耗时: ${duration1}ms`)
    console.log(`第二次下载耗时: ${duration2}ms`)
    
    if (duration2 < duration1 * 0.1) { // 缓存应该快很多
      console.log('✅ 缓存功能正常')
      return true
    } else {
      console.log('❌ 缓存功能异常')
      return false
    }
  }

  // 测试批量下载
  async testBatchDownload() {
    console.log('开始测试批量下载...')
    
    const urls = [
      'https://picsum.photos/80/80?random=batch1',
      'https://picsum.photos/80/80?random=batch2',
      'https://picsum.photos/80/80?random=batch3'
    ]
    
    const startTime = Date.now()
    const results = await imageHelper.downloadImages(urls)
    const endTime = Date.now()
    
    const successCount = results.filter(r => r.success).length
    const totalTime = endTime - startTime
    
    console.log(`批量下载结果: ${successCount}/${urls.length} 成功`)
    console.log(`总耗时: ${totalTime}ms`)
    console.log(`平均耗时: ${(totalTime / urls.length).toFixed(1)}ms`)
    
    return {
      total: urls.length,
      success: successCount,
      duration: totalTime,
      results: results
    }
  }

  // 打印测试结果
  printResults() {
    console.log('\n=== 头像测试结果汇总 ===')
    
    const totalTests = this.testResults.length
    const passedTests = this.testResults.filter(r => r.passed).length
    const failedTests = totalTests - passedTests
    
    console.log(`总测试数: ${totalTests}`)
    console.log(`通过: ${passedTests}`)
    console.log(`失败: ${failedTests}`)
    console.log(`通过率: ${(passedTests / totalTests * 100).toFixed(1)}%`)
    
    if (failedTests > 0) {
      console.log('\n失败的测试:')
      this.testResults.filter(r => !r.passed).forEach(result => {
        console.log(`- ${result.name}: ${result.error || '未知错误'}`)
      })
    }
    
    // 性能统计
    const durations = this.testResults.map(r => r.duration)
    const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length
    const maxDuration = Math.max(...durations)
    const minDuration = Math.min(...durations)
    
    console.log('\n性能统计:')
    console.log(`平均耗时: ${avgDuration.toFixed(1)}ms`)
    console.log(`最长耗时: ${maxDuration}ms`)
    console.log(`最短耗时: ${minDuration}ms`)
  }

  // 生成测试报告
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.testResults.length,
        passed: this.testResults.filter(r => r.passed).length,
        failed: this.testResults.filter(r => !r.passed).length,
        passRate: (this.testResults.filter(r => r.passed).length / this.testResults.length * 100).toFixed(1) + '%'
      },
      performance: {
        avgDuration: (this.testResults.reduce((sum, r) => sum + r.duration, 0) / this.testResults.length).toFixed(1) + 'ms',
        maxDuration: Math.max(...this.testResults.map(r => r.duration)) + 'ms',
        minDuration: Math.min(...this.testResults.map(r => r.duration)) + 'ms'
      },
      details: this.testResults,
      cacheInfo: imageHelper.getCacheInfo()
    }
    
    return report
  }

  // 清理测试数据
  cleanup() {
    console.log('清理测试数据...')
    imageHelper.clearCache()
    this.testResults = []
  }
}

// 快速测试方法
export async function quickAvatarTest() {
  const tester = new AvatarTester()
  
  try {
    console.log('🚀 开始头像功能快速测试...')
    
    // 测试URL处理
    await tester.testAvatarUrls()
    
    // 测试缓存
    await tester.testCaching()
    
    // 测试批量下载
    await tester.testBatchDownload()
    
    // 生成报告
    const report = tester.generateReport()
    console.log('\n📊 测试报告:', report)
    
    // 显示结果给用户
    uni.showModal({
      title: '头像测试完成',
      content: `通过率: ${report.summary.passRate}\n平均耗时: ${report.performance.avgDuration}`,
      showCancel: false
    })
    
    return report
    
  } catch (error) {
    console.error('测试过程出错:', error)
    uni.showToast({
      title: '测试失败',
      icon: 'none'
    })
    return null
  } finally {
    tester.cleanup()
  }
}

// 使用示例:
// import { quickAvatarTest, AvatarTester } from '@/utils/avatarTester.js'
// 
// // 快速测试
// await quickAvatarTest()
// 
// // 详细测试
// const tester = new AvatarTester()
// await tester.testAvatarUrls()
// const report = tester.generateReport()
// console.log(report)
