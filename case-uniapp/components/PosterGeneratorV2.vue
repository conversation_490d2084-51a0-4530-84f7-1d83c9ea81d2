<template>
  <view class="poster-generator">
    <!-- 海报预览区域 - 完全匹配案例详情页样式 -->
    <view class="poster-preview" :id="'poster-preview-' + caseId" v-if="visible">
      <view class="case-detail">
        <!-- 用户信息 -->
        <view class="user-section" :id="'user-section-' + caseId">
          <image class="user-avatar" :src="userInfo.avatar" mode="aspectFill" :id="'user-avatar-' + caseId"></image>
          <view class="user-info">
            <text class="user-name" :id="'user-name-' + caseId">{{ userInfo.nickName }}</text>
            <text class="publish-time" :id="'publish-time-' + caseId">{{ publishTime }}</text>
          </view>
        </view>

        <!-- 案例标签 -->
        <view class="case-tags" v-if="tags && tags.length > 0" :id="'case-tags-' + caseId">
          <text class="tag" v-for="tag in tags" :key="tag">#{{ tag }}</text>
        </view>

        <!-- 案例标题 -->
        <view class="case-title" :id="'case-title-' + caseId">{{ title }}</view>

        <!-- 案例内容 -->
        <view class="case-content" :id="'case-content-' + caseId">
          <rich-text :nodes="content"></rich-text>
        </view>

        <!-- 案例图片 -->
        <view class="case-images" v-if="showImages && images && images.length > 0" :id="'case-images-' + caseId">
          <view class="image-list">
            <view class="image-item" v-for="(image, index) in displayImages" :key="index">
              <image class="case-image" :src="image" mode="widthFix"></image>
            </view>
          </view>
        </view>

        <!-- 点击统计 -->
        <view class="stats-section" :id="'stats-section-' + caseId">
          <view class="stats-item">
            <text class="stats-label">{{ templateType }}</text>
            <text class="stats-label">阅读{{ clickCount }}次</text>
          </view>
        </view>

        <!-- 二维码区域 -->
        <view class="qr-section" :id="'qr-section-' + caseId">
          <view class="qr-text">扫码查看完整内容</view>
          <view class="qr-placeholder">
            <text class="qr-icon">⊞</text>
          </view>
        </view>

        <!-- 品牌标识 -->
        <view class="brand-section" :id="'brand-section-' + caseId">
          <text class="brand-text">案例管理系统</text>
        </view>
      </view>
    </view>

    <!-- Canvas用于生成图片 -->
    <canvas 
      class="hidden-canvas" 
      canvas-id="posterCanvas" 
      :style="canvasStyle"
    ></canvas>
  </view>
</template>

<script>
export default {
  name: 'PosterGeneratorV2',
  props: {
    visible: { type: Boolean, default: false },
    caseId: { type: [String, Number], required: true },
    userInfo: { type: Object, required: true },
    title: { type: String, required: true },
    content: { type: String, required: true },
    tags: { type: Array, default: () => [] },
    images: { type: Array, default: () => [] },
    templateType: { type: String, default: '' },
    clickCount: { type: Number, default: 0 },
    publishTime: { type: String, default: '' },
    showImages: { type: Boolean, default: true },
    imageLimit: { type: Number, default: 3 }
  },

  computed: {
    displayImages() {
      return this.images.slice(0, this.imageLimit)
    },
    
    canvasStyle() {
      return {
        position: 'fixed',
        top: '-9999px',
        left: '-9999px',
        width: '750px',
        height: '2000px', // 足够大的高度
        zIndex: -1
      }
    }
  },

  methods: {
    // 生成海报 - 1:1还原方案
    async generatePoster() {
      try {
        console.log('开始生成1:1还原海报...')
        uni.showLoading({ title: '生成海报中...' })

        // 等待DOM渲染完成
        await this.$nextTick()
        await this.waitForImagesLoad()

        // 获取DOM结构信息
        const domInfo = await this.analyzeDOMStructure()
        console.log('DOM结构分析完成:', domInfo)

        // 使用DOM信息绘制Canvas
        const result = await this.renderCanvasFromDOM(domInfo)
        
        uni.hideLoading()
        return result
        
      } catch (error) {
        uni.hideLoading()
        console.error('生成海报失败:', error)
        throw error
      }
    },

    // 等待图片加载
    waitForImagesLoad() {
      return new Promise((resolve) => {
        setTimeout(resolve, 1500) // 简单延时等待
      })
    },

    // 分析DOM结构 - 获取所有元素的精确位置和样式
    analyzeDOMStructure() {
      return new Promise((resolve, reject) => {
        const query = uni.createSelectorQuery().in(this)
        
        // 获取主容器信息
        query.select(`#poster-preview-${this.caseId}`).boundingClientRect()
        
        // 获取各个区域的信息
        query.select(`#user-section-${this.caseId}`).boundingClientRect()
        query.select(`#user-avatar-${this.caseId}`).boundingClientRect()
        query.select(`#user-name-${this.caseId}`).boundingClientRect()
        query.select(`#publish-time-${this.caseId}`).boundingClientRect()
        query.select(`#case-tags-${this.caseId}`).boundingClientRect()
        query.select(`#case-title-${this.caseId}`).boundingClientRect()
        query.select(`#case-content-${this.caseId}`).boundingClientRect()
        query.select(`#case-images-${this.caseId}`).boundingClientRect()
        query.select(`#stats-section-${this.caseId}`).boundingClientRect()
        query.select(`#qr-section-${this.caseId}`).boundingClientRect()
        query.select(`#brand-section-${this.caseId}`).boundingClientRect()

        query.exec((res) => {
          if (res && res[0]) {
            const domInfo = {
              container: res[0],
              userSection: res[1],
              userAvatar: res[2],
              userName: res[3],
              publishTime: res[4],
              caseTags: res[5],
              caseTitle: res[6],
              caseContent: res[7],
              caseImages: res[8],
              statsSection: res[9],
              qrSection: res[10],
              brandSection: res[11]
            }
            resolve(domInfo)
          } else {
            reject(new Error('无法获取DOM信息'))
          }
        })
      })
    },

    // 根据DOM信息渲染Canvas
    renderCanvasFromDOM(domInfo) {
      return new Promise((resolve, reject) => {
        const ctx = uni.createCanvasContext('posterCanvas', this)
        const container = domInfo.container
        
        if (!container) {
          reject(new Error('无法获取容器信息'))
          return
        }

        // 设置Canvas尺寸
        const canvasWidth = 750 // 标准宽度
        const canvasHeight = Math.ceil(container.height * 2) // 转换rpx到px
        
        console.log('Canvas尺寸:', canvasWidth, canvasHeight)

        // 清空并设置背景
        ctx.clearRect(0, 0, canvasWidth, canvasHeight)
        ctx.setFillStyle('#ffffff')
        ctx.fillRect(0, 0, canvasWidth, canvasHeight)

        // 绘制所有元素
        this.drawAllElements(ctx, domInfo, container)

        // 执行绘制
        ctx.draw(false, () => {
          setTimeout(() => {
            uni.canvasToTempFilePath({
              canvasId: 'posterCanvas',
              width: canvasWidth,
              height: canvasHeight,
              success: (res) => {
                console.log('Canvas转图片成功:', res)
                resolve(res)
              },
              fail: (error) => {
                console.error('Canvas转图片失败:', error)
                reject(error)
              }
            }, this)
          }, 1000)
        })
      })
    },

    // 绘制所有元素
    async drawAllElements(ctx, domInfo, container) {
      const scale = 750 / uni.getSystemInfoSync().windowWidth // rpx转px比例
      
      try {
        // 绘制用户头像
        if (domInfo.userAvatar) {
          await this.drawUserAvatar(ctx, domInfo.userAvatar, container, scale)
        }

        // 绘制用户名
        if (domInfo.userName) {
          this.drawUserName(ctx, domInfo.userName, container, scale)
        }

        // 绘制发布时间
        if (domInfo.publishTime) {
          this.drawPublishTime(ctx, domInfo.publishTime, container, scale)
        }

        // 绘制标签
        if (domInfo.caseTags && this.tags.length > 0) {
          this.drawCaseTags(ctx, domInfo.caseTags, container, scale)
        }

        // 绘制标题
        if (domInfo.caseTitle) {
          this.drawCaseTitle(ctx, domInfo.caseTitle, container, scale)
        }

        // 绘制内容
        if (domInfo.caseContent) {
          this.drawCaseContent(ctx, domInfo.caseContent, container, scale)
        }

        // 绘制统计信息
        if (domInfo.statsSection) {
          this.drawStatsSection(ctx, domInfo.statsSection, container, scale)
        }

        // 绘制二维码区域
        if (domInfo.qrSection) {
          this.drawQRSection(ctx, domInfo.qrSection, container, scale)
        }

        // 绘制品牌信息
        if (domInfo.brandSection) {
          this.drawBrandSection(ctx, domInfo.brandSection, container, scale)
        }

      } catch (error) {
        console.error('绘制元素时出错:', error)
      }
    },

    // 计算相对位置
    getRelativePosition(element, container, scale) {
      return {
        x: (element.left - container.left) * scale,
        y: (element.top - container.top) * scale,
        width: element.width * scale,
        height: element.height * scale
      }
    },

    // 绘制用户头像
    async drawUserAvatar(ctx, avatarRect, container, scale) {
      const pos = this.getRelativePosition(avatarRect, container, scale)
      
      try {
        // 这里需要实现图片绘制逻辑
        ctx.setFillStyle('#f0f0f0')
        ctx.fillRect(pos.x, pos.y, pos.width, pos.height)
        
        // 绘制圆形边框
        ctx.beginPath()
        ctx.arc(pos.x + pos.width/2, pos.y + pos.height/2, pos.width/2, 0, 2 * Math.PI)
        ctx.setStrokeStyle('#e0e0e0')
        ctx.setLineWidth(1)
        ctx.stroke()
      } catch (error) {
        console.error('绘制头像失败:', error)
      }
    },

    // 绘制用户名
    drawUserName(ctx, nameRect, container, scale) {
      const pos = this.getRelativePosition(nameRect, container, scale)
      
      ctx.setFillStyle('#333333')
      ctx.setFontSize(32 * scale)
      ctx.setTextAlign('left')
      ctx.fillText(this.userInfo.nickName, pos.x, pos.y + pos.height * 0.7)
    },

    // 绘制发布时间
    drawPublishTime(ctx, timeRect, container, scale) {
      const pos = this.getRelativePosition(timeRect, container, scale)
      
      ctx.setFillStyle('#999999')
      ctx.setFontSize(26 * scale)
      ctx.setTextAlign('left')
      ctx.fillText(this.publishTime, pos.x, pos.y + pos.height * 0.7)
    },

    // 绘制标签
    drawCaseTags(ctx, tagsRect, container, scale) {
      const pos = this.getRelativePosition(tagsRect, container, scale)
      
      let currentX = pos.x
      const tagHeight = 32 * scale
      const tagSpacing = 12 * scale
      
      ctx.setFontSize(24 * scale)
      
      this.tags.forEach(tag => {
        const tagText = `#${tag}`
        const textWidth = ctx.measureText(tagText).width
        const tagWidth = textWidth + 32 * scale
        
        // 绘制标签背景
        ctx.setFillStyle('#f0f8ff')
        ctx.fillRect(currentX, pos.y, tagWidth, tagHeight)
        
        // 绘制标签文字
        ctx.setFillStyle('#007AFF')
        ctx.fillText(tagText, currentX + 16 * scale, pos.y + tagHeight * 0.7)
        
        currentX += tagWidth + tagSpacing
      })
    },

    // 绘制标题
    drawCaseTitle(ctx, titleRect, container, scale) {
      const pos = this.getRelativePosition(titleRect, container, scale)
      
      ctx.setFillStyle('#333333')
      ctx.setFontSize(36 * scale)
      ctx.setTextAlign('left')
      
      // 简单处理，实际应该考虑换行
      ctx.fillText(this.title, pos.x, pos.y + pos.height * 0.3)
    },

    // 绘制内容
    drawCaseContent(ctx, contentRect, container, scale) {
      const pos = this.getRelativePosition(contentRect, container, scale)
      
      ctx.setFillStyle('#666666')
      ctx.setFontSize(30 * scale)
      ctx.setTextAlign('left')
      
      // 去除HTML标签
      const plainText = this.content.replace(/<[^>]*>/g, '')
      const maxLength = 200
      const displayText = plainText.length > maxLength ? 
        plainText.substring(0, maxLength) + '...' : plainText
      
      // 简单处理，实际应该考虑换行
      ctx.fillText(displayText, pos.x, pos.y + pos.height * 0.2)
    },

    // 绘制统计信息
    drawStatsSection(ctx, statsRect, container, scale) {
      const pos = this.getRelativePosition(statsRect, container, scale)
      
      // 绘制背景
      ctx.setFillStyle('#f8f9fa')
      ctx.fillRect(pos.x, pos.y, pos.width, pos.height)
      
      // 绘制文字
      ctx.setFillStyle('#666666')
      ctx.setFontSize(28 * scale)
      ctx.setTextAlign('left')
      ctx.fillText(this.templateType, pos.x + 20 * scale, pos.y + pos.height * 0.6)
      
      ctx.setTextAlign('right')
      ctx.fillText(`阅读${this.clickCount}次`, pos.x + pos.width - 20 * scale, pos.y + pos.height * 0.6)
    },

    // 绘制二维码区域
    drawQRSection(ctx, qrRect, container, scale) {
      const pos = this.getRelativePosition(qrRect, container, scale)
      
      // 绘制分割线
      ctx.setStrokeStyle('#f0f0f0')
      ctx.setLineWidth(1)
      ctx.beginPath()
      ctx.moveTo(pos.x, pos.y)
      ctx.lineTo(pos.x + pos.width, pos.y)
      ctx.stroke()
      
      // 绘制二维码占位符
      const qrSize = 80 * scale
      const qrX = pos.x + pos.width - qrSize - 20 * scale
      const qrY = pos.y + 20 * scale
      
      ctx.setFillStyle('#f5f5f5')
      ctx.fillRect(qrX, qrY, qrSize, qrSize)
      ctx.setStrokeStyle('#e0e0e0')
      ctx.strokeRect(qrX, qrY, qrSize, qrSize)
      
      // 绘制说明文字
      ctx.setFillStyle('#666666')
      ctx.setFontSize(24 * scale)
      ctx.setTextAlign('left')
      ctx.fillText('扫码查看完整内容', pos.x + 20 * scale, pos.y + pos.height * 0.6)
    },

    // 绘制品牌信息
    drawBrandSection(ctx, brandRect, container, scale) {
      const pos = this.getRelativePosition(brandRect, container, scale)
      
      ctx.setFillStyle('#999999')
      ctx.setFontSize(22 * scale)
      ctx.setTextAlign('center')
      ctx.fillText('案例管理系统', pos.x + pos.width / 2, pos.y + pos.height * 0.6)
    }
  }
}
</script>

<style scoped>
/* 完全复制案例详情页的样式 */
.poster-generator {
  position: relative;
}

.poster-preview {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
  margin: 20rpx;
}

.case-detail {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

/* 用户信息 */
.user-section {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.publish-time {
  font-size: 26rpx;
  color: #999;
}

/* 案例标签 */
.case-tags {
  margin-bottom: 20rpx;
}

.tag {
  display: inline-block;
  background-color: #f0f8ff;
  color: #007AFF;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  margin-right: 12rpx;
  margin-bottom: 10rpx;
}

/* 案例标题 */
.case-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 30rpx;
}

/* 案例内容 */
.case-content {
  font-size: 30rpx;
  color: #666;
  line-height: 1.8;
  margin-bottom: 30rpx;
}

/* 案例图片 */
.case-images {
  margin-bottom: 30rpx;
}

.image-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.image-item {
  width: 100%;
  border-radius: 12rpx;
  overflow: hidden;
}

.case-image {
  width: 100%;
  display: block;
}

/* 统计信息 */
.stats-section {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-label {
  font-size: 28rpx;
  color: #666;
}

/* 二维码区域 */
.qr-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.qr-text {
  font-size: 24rpx;
  color: #666;
}

.qr-placeholder {
  width: 80rpx;
  height: 80rpx;
  background-color: #f5f5f5;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-icon {
  font-size: 40rpx;
  color: #ccc;
}

/* 品牌标识 */
.brand-section {
  text-align: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.brand-text {
  font-size: 22rpx;
  color: #999;
}

/* 隐藏的canvas */
.hidden-canvas {
  position: fixed;
  top: -9999px;
  left: -9999px;
  opacity: 0;
  pointer-events: none;
}
</style>
