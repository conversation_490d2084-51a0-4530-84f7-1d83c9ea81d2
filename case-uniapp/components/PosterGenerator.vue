<template>
  <view class="poster-generator">
    <!-- 海报预览区域 -->
    <view class="poster-preview" :id="'poster-preview-' + caseId" v-if="visible">
      <view class="poster-content" :style="posterStyle">
        <!-- 用户信息 -->
        <view class="user-section">
          <image class="user-avatar" :src="userInfo.avatar" mode="aspectFill"></image>
          <view class="user-info">
            <text class="user-name">{{ userInfo.nickName }}</text>
            <text class="publish-time">{{ publishTime }}</text>
          </view>
        </view>

        <!-- 案例标签 -->
        <view class="case-tags" v-if="tags && tags.length > 0">
          <text class="tag" v-for="tag in tags" :key="tag">#{{ tag }}</text>
        </view>

        <!-- 案例标题 -->
        <view class="case-title">{{ title }}</view>

        <!-- 案例内容 -->
        <view class="case-content">
          <rich-text :nodes="content"></rich-text>
        </view>

        <!-- 案例图片 -->
        <view class="case-images" v-if="showImages && images && images.length > 0">
          <view class="image-grid" :class="'grid-' + Math.min(images.length, 3)">
            <view class="image-item" v-for="(image, index) in displayImages" :key="index">
              <image class="case-image" :src="image" mode="aspectFill"></image>
            </view>
          </view>
        </view>

        <!-- 统计信息 -->
        <view class="stats-section">
          <view class="stats-item">
            <text class="stats-label">{{ templateType }}</text>
            <text class="stats-label">阅读{{ clickCount }}次</text>
          </view>
        </view>

        <!-- 二维码区域 -->
        <view class="qr-section">
          <view class="qr-text">扫码查看完整内容</view>
          <view class="qr-placeholder">
            <text class="qr-icon">⊞</text>
          </view>
        </view>

        <!-- 品牌标识 -->
        <view class="brand-section">
          <text class="brand-text">案例管理系统</text>
        </view>
      </view>
    </view>

    <!-- 隐藏的canvas用于生成图片 -->
    <canvas 
      class="hidden-canvas" 
      canvas-id="posterCanvas" 
      :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
    ></canvas>
  </view>
</template>

<script>
export default {
  name: 'PosterGenerator',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    caseId: {
      type: [String, Number],
      required: true
    },
    userInfo: {
      type: Object,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    content: {
      type: String,
      required: true
    },
    tags: {
      type: Array,
      default: () => []
    },
    images: {
      type: Array,
      default: () => []
    },
    templateType: {
      type: String,
      default: ''
    },
    clickCount: {
      type: Number,
      default: 0
    },
    publishTime: {
      type: String,
      default: ''
    },
    showImages: {
      type: Boolean,
      default: true
    },
    imageLimit: {
      type: Number,
      default: 3
    },
    posterWidth: {
      type: Number,
      default: 750
    },
    posterHeight: {
      type: Number,
      default: 1334
    }
  },

  computed: {
    displayImages() {
      return this.images.slice(0, this.imageLimit)
    },
    
    posterStyle() {
      return {
        width: this.posterWidth + 'px',
        minHeight: this.posterHeight + 'px',
        backgroundColor: '#ffffff'
      }
    },
    
    canvasWidth() {
      return this.posterWidth
    },
    
    canvasHeight() {
      return this.posterHeight
    }
  },

  methods: {
    // 生成海报
    async generatePoster() {
      try {
        uni.showLoading({
          title: '生成海报中...'
        })

        // 等待DOM渲染完成
        await this.$nextTick()
        await this.waitForImagesLoad()

        // 使用canvas生成海报
        const result = await this.renderToCanvas()
        
        uni.hideLoading()
        return result
        
      } catch (error) {
        uni.hideLoading()
        console.error('生成海报失败:', error)
        throw error
      }
    },

    // 等待图片加载完成
    waitForImagesLoad() {
      return new Promise((resolve) => {
        if (!this.showImages || !this.displayImages.length) {
          resolve()
          return
        }

        let loadedCount = 0
        const totalImages = this.displayImages.length + 1 // +1 for avatar

        const checkAllLoaded = () => {
          loadedCount++
          if (loadedCount >= totalImages) {
            resolve()
          }
        }

        // 简单的延时等待，实际项目中可以监听图片load事件
        setTimeout(resolve, 1500)
      })
    },

    // 渲染到Canvas
    renderToCanvas() {
      return new Promise((resolve, reject) => {
        const ctx = uni.createCanvasContext('posterCanvas', this)
        
        // 获取预览区域的尺寸
        const query = uni.createSelectorQuery().in(this)
        query.select(`#poster-preview-${this.caseId}`).boundingClientRect((rect) => {
          if (!rect) {
            reject(new Error('无法获取海报预览区域'))
            return
          }

          // 清空画布
          ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight)
          
          // 设置背景色
          ctx.setFillStyle('#ffffff')
          ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)

          // 绘制海报内容
          this.drawPosterContent(ctx)

          // 执行绘制
          ctx.draw(false, () => {
            setTimeout(() => {
              uni.canvasToTempFilePath({
                canvasId: 'posterCanvas',
                success: resolve,
                fail: reject
              }, this)
            }, 500)
          })
        }).exec()
      })
    },

    // 绘制海报内容
    drawPosterContent(ctx) {
      const padding = 40
      let currentY = padding

      // 绘制用户信息
      currentY = this.drawUserSection(ctx, currentY, padding)
      
      // 绘制标签
      if (this.tags && this.tags.length > 0) {
        currentY = this.drawTags(ctx, currentY, padding)
      }
      
      // 绘制标题
      currentY = this.drawTitle(ctx, currentY, padding)
      
      // 绘制内容
      currentY = this.drawContent(ctx, currentY, padding)
      
      // 绘制统计信息
      currentY = this.drawStats(ctx, currentY, padding)
      
      // 绘制二维码和品牌信息
      this.drawFooter(ctx, padding)
    },

    // 绘制用户信息
    drawUserSection(ctx, startY, padding) {
      const avatarSize = 60
      const spacing = 20

      // 绘制用户名
      ctx.setFillStyle('#333333')
      ctx.setFontSize(28)
      ctx.setTextAlign('left')
      ctx.fillText(this.userInfo.nickName, padding + avatarSize + spacing, startY + 25)

      // 绘制发布时间
      ctx.setFillStyle('#999999')
      ctx.setFontSize(22)
      ctx.fillText(this.publishTime, padding + avatarSize + spacing, startY + 55)

      return startY + avatarSize + 30
    },

    // 绘制标签
    drawTags(ctx, startY, padding) {
      if (!this.tags || this.tags.length === 0) return startY

      let currentX = padding
      const tagHeight = 35
      const tagSpacing = 15

      ctx.setFillStyle('#007AFF')
      ctx.setFontSize(22)

      this.tags.forEach(tag => {
        const tagText = `#${tag}`
        const textWidth = ctx.measureText(tagText).width
        const tagWidth = textWidth + 20

        // 绘制标签文字
        ctx.fillText(tagText, currentX, startY + 25)
        currentX += tagWidth + tagSpacing
      })

      return startY + tagHeight + 20
    },

    // 绘制标题
    drawTitle(ctx, startY, padding) {
      ctx.setFillStyle('#000000')
      ctx.setFontSize(36)
      ctx.setTextAlign('left')

      const lines = this.wrapText(ctx, this.title, this.canvasWidth - padding * 2)
      const lineHeight = 50

      lines.forEach((line, index) => {
        ctx.fillText(line, padding, startY + (index + 1) * lineHeight)
      })

      return startY + lines.length * lineHeight + 30
    },

    // 绘制内容
    drawContent(ctx, startY, padding) {
      // 去除HTML标签
      const plainText = this.content.replace(/<[^>]*>/g, '')
      const maxLength = 400
      const displayText = plainText.length > maxLength ? 
        plainText.substring(0, maxLength) + '...' : plainText

      ctx.setFillStyle('#333333')
      ctx.setFontSize(28)

      const lines = this.wrapText(ctx, displayText, this.canvasWidth - padding * 2)
      const lineHeight = 40
      const maxLines = 12

      const displayLines = lines.slice(0, maxLines)
      displayLines.forEach((line, index) => {
        ctx.fillText(line, padding, startY + (index + 1) * lineHeight)
      })

      return startY + displayLines.length * lineHeight + 40
    },

    // 绘制统计信息
    drawStats(ctx, startY, padding) {
      // 绘制分割线
      ctx.setStrokeStyle('#f0f0f0')
      ctx.setLineWidth(2)
      ctx.beginPath()
      ctx.moveTo(padding, startY)
      ctx.lineTo(this.canvasWidth - padding, startY)
      ctx.stroke()

      const statsY = startY + 30

      ctx.setFillStyle('#666666')
      ctx.setFontSize(24)
      ctx.setTextAlign('left')
      ctx.fillText(this.templateType, padding, statsY)

      ctx.setTextAlign('right')
      ctx.fillText(`阅读 ${this.clickCount} 次`, this.canvasWidth - padding, statsY)

      return statsY + 50
    },

    // 绘制底部信息
    drawFooter(ctx, padding) {
      const footerY = this.canvasHeight - 120

      // 绘制二维码占位符
      const qrSize = 80
      const qrX = this.canvasWidth - padding - qrSize
      
      ctx.setFillStyle('#f5f5f5')
      ctx.fillRect(qrX, footerY, qrSize, qrSize)
      
      ctx.setStrokeStyle('#e0e0e0')
      ctx.setLineWidth(2)
      ctx.strokeRect(qrX, footerY, qrSize, qrSize)

      // 绘制二维码说明
      ctx.setFillStyle('#666666')
      ctx.setFontSize(20)
      ctx.setTextAlign('center')
      ctx.fillText('扫码查看', qrX + qrSize / 2, footerY + qrSize + 25)

      // 绘制品牌信息
      ctx.setFillStyle('#999999')
      ctx.setFontSize(22)
      ctx.setTextAlign('left')
      ctx.fillText('案例管理系统', padding, footerY + qrSize - 10)
    },

    // 文字换行处理
    wrapText(ctx, text, maxWidth) {
      const words = text.split('')
      const lines = []
      let currentLine = ''

      for (let i = 0; i < words.length; i++) {
        const testLine = currentLine + words[i]
        const metrics = ctx.measureText(testLine)
        const testWidth = metrics.width

        if (testWidth > maxWidth && i > 0) {
          lines.push(currentLine)
          currentLine = words[i]
        } else {
          currentLine = testLine
        }
      }
      lines.push(currentLine)
      return lines
    }
  }
}
</script>

<style scoped>
.poster-generator {
  position: relative;
}

.poster-preview {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
  margin: 20rpx;
}

.poster-content {
  background-color: #ffffff;
  padding: 40rpx;
  position: relative;
}

/* 用户信息 */
.user-section {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.publish-time {
  font-size: 22rpx;
  color: #999;
}

/* 标签 */
.case-tags {
  margin-bottom: 30rpx;
}

.tag {
  display: inline-block;
  background-color: #e3f2fd;
  color: #1976d2;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  margin-bottom: 8rpx;
}

/* 标题 */
.case-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 30rpx;
}

/* 内容 */
.case-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

/* 图片 */
.case-images {
  margin-bottom: 30rpx;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.image-grid.grid-1 .image-item {
  width: 100%;
}

.image-grid.grid-2 .image-item {
  width: calc(50% - 5rpx);
}

.image-grid.grid-3 .image-item {
  width: calc(33.33% - 7rpx);
}

.case-image {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
}

/* 统计信息 */
.stats-section {
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  margin-bottom: 40rpx;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

/* 二维码区域 */
.qr-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.qr-text {
  font-size: 24rpx;
  color: #666;
}

.qr-placeholder {
  width: 80rpx;
  height: 80rpx;
  background-color: #f5f5f5;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-icon {
  font-size: 40rpx;
  color: #ccc;
}

/* 品牌标识 */
.brand-section {
  text-align: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.brand-text {
  font-size: 22rpx;
  color: #999;
}

/* 隐藏的canvas */
.hidden-canvas {
  position: fixed;
  top: -9999px;
  left: -9999px;
  opacity: 0;
  pointer-events: none;
}
</style>
