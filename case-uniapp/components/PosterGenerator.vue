<template>
  <view class="poster-generator">
    <!-- 海报预览区域 -->
    <view class="poster-preview" :id="'poster-preview-' + caseId" v-if="visible">
      <view class="poster-content" :style="posterStyle">
        <!-- 用户信息 -->
        <view class="user-section">
          <image class="user-avatar" :src="userInfo.avatar" mode="aspectFill"></image>
          <view class="user-info">
            <text class="user-name">{{ userInfo.nickName }}</text>
            <text class="publish-time">{{ publishTime }}</text>
          </view>
        </view>

        <!-- 案例标签 -->
        <view class="case-tags" v-if="tags && tags.length > 0">
          <text class="tag" v-for="tag in tags" :key="tag">#{{ tag }}</text>
        </view>

        <!-- 案例标题 -->
        <view class="case-title">{{ title }}</view>

        <!-- 案例内容 -->
        <view class="case-content">
          <rich-text :nodes="content"></rich-text>
        </view>

        <!-- 案例图片 -->
        <view class="case-images" v-if="showImages && images && images.length > 0">
          <view class="image-grid" :class="'grid-' + Math.min(images.length, 3)">
            <view class="image-item" v-for="(image, index) in displayImages" :key="index">
              <image class="case-image" :src="image" mode="aspectFill"></image>
            </view>
          </view>
        </view>

        <!-- 统计信息 -->
        <view class="stats-section">
          <view class="stats-item">
            <text class="stats-label">{{ templateType }}</text>
            <text class="stats-label">阅读{{ clickCount }}次</text>
          </view>
        </view>

        <!-- 二维码区域 -->
        <view class="qr-section">
          <view class="qr-text">扫码查看完整内容</view>
          <view class="qr-placeholder">
            <text class="qr-icon">⊞</text>
          </view>
        </view>

        <!-- 品牌标识 -->
        <view class="brand-section">
          <text class="brand-text">案例管理系统</text>
        </view>
      </view>
    </view>

    <!-- 隐藏的canvas用于生成图片 -->
    <canvas class="hidden-canvas" canvas-id="posterCanvas" :style="{
      width: canvasWidth + 'px',
      height: canvasHeight + 'px',
      position: 'fixed',
      top: '-9999px',
      left: '-9999px',
      zIndex: -1
    }"></canvas>
  </view>
</template>

<script>
export default {
  name: 'PosterGenerator',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    caseId: {
      type: [String, Number],
      required: true
    },
    userInfo: {
      type: Object,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    content: {
      type: String,
      required: true
    },
    tags: {
      type: Array,
      default: () => []
    },
    images: {
      type: Array,
      default: () => []
    },
    templateType: {
      type: String,
      default: ''
    },
    clickCount: {
      type: Number,
      default: 0
    },
    publishTime: {
      type: String,
      default: ''
    },
    showImages: {
      type: Boolean,
      default: true
    },
    imageLimit: {
      type: Number,
      default: 3
    },
    posterWidth: {
      type: Number,
      default: 750
    },
    posterHeight: {
      type: Number,
      default: 1334
    }
  },

  computed: {
    displayImages() {
      return this.images.slice(0, this.imageLimit)
    },

    posterStyle() {
      return {
        width: this.posterWidth + 'px',
        minHeight: this.posterHeight + 'px',
        backgroundColor: '#ffffff'
      }
    },

    canvasWidth() {
      return this.posterWidth
    },

    canvasHeight() {
      return this.posterHeight
    }
  },

  methods: {
    // 生成海报
    async generatePoster() {
      try {
        console.log('开始生成海报...')
        uni.showLoading({
          title: '生成海报中...'
        })

        // 等待DOM渲染完成
        await this.$nextTick()
        console.log('DOM渲染完成')

        await this.waitForImagesLoad()
        console.log('图片加载完成')

        // 使用canvas生成海报
        const result = await this.renderToCanvas()
        console.log('Canvas渲染完成:', result)

        uni.hideLoading()
        return result

      } catch (error) {
        uni.hideLoading()
        console.error('生成海报失败:', error)
        uni.showToast({
          title: '生成海报失败: ' + error.message,
          icon: 'none',
          duration: 3000
        })
        throw error
      }
    },

    // 等待图片加载完成
    waitForImagesLoad() {
      return new Promise((resolve) => {
        if (!this.showImages || !this.displayImages.length) {
          resolve()
          return
        }

        let loadedCount = 0
        const totalImages = this.displayImages.length + 1 // +1 for avatar

        const checkAllLoaded = () => {
          loadedCount++
          if (loadedCount >= totalImages) {
            resolve()
          }
        }

        // 简单的延时等待，实际项目中可以监听图片load事件
        setTimeout(resolve, 1500)
      })
    },

    // 渲染到Canvas - 使用DOM截图方式实现1:1还原
    renderToCanvas() {
      return new Promise((resolve, reject) => {
        // 首先尝试使用DOM截图方式
        this.renderDOMToCanvas()
          .then(resolve)
          .catch((error) => {
            console.warn('DOM截图失败，使用Canvas绘制方式:', error)
            // 降级到Canvas绘制方式
            this.renderCanvasDrawing()
              .then(resolve)
              .catch(reject)
          })
      })
    },

    // DOM截图方式 - 真正的1:1还原
    renderDOMToCanvas() {
      return new Promise((resolve, reject) => {
        const query = uni.createSelectorQuery().in(this)
        query.select(`#poster-preview-${this.caseId}`).boundingClientRect((rect) => {
          if (!rect) {
            reject(new Error('无法获取海报预览区域'))
            return
          }

          console.log('DOM区域信息:', rect)

          // 使用uni.canvasToTempFilePath的node参数来截图DOM
          // 注意：这个功能在某些平台可能不支持
          const ctx = uni.createCanvasContext('posterCanvas', this)

          // 设置Canvas尺寸匹配DOM
          const scale = 2 // 提高清晰度
          const canvasWidth = rect.width * scale
          const canvasHeight = rect.height * scale

          ctx.clearRect(0, 0, canvasWidth, canvasHeight)
          ctx.scale(scale, scale)

          // 尝试使用HTML5 Canvas的drawImage功能
          // 这需要将DOM元素转换为图片
          this.convertDOMToImage(rect)
            .then((imageData) => {
              ctx.drawImage(imageData, 0, 0, rect.width, rect.height)
              ctx.draw(false, () => {
                setTimeout(() => {
                  uni.canvasToTempFilePath({
                    canvasId: 'posterCanvas',
                    width: canvasWidth,
                    height: canvasHeight,
                    success: resolve,
                    fail: reject
                  }, this)
                }, 500)
              })
            })
            .catch(reject)
        }).exec()
      })
    },

    // Canvas绘制方式 - 降级方案
    renderCanvasDrawing() {
      return new Promise((resolve, reject) => {
        const ctx = uni.createCanvasContext('posterCanvas', this)

        // 获取预览区域的尺寸
        const query = uni.createSelectorQuery().in(this)
        query.select(`#poster-preview-${this.caseId}`).boundingClientRect((rect) => {
          if (!rect) {
            reject(new Error('无法获取海报预览区域'))
            return
          }

          // 使用实际DOM高度
          const actualHeight = Math.ceil(rect.height * 2) // rpx to px conversion

          // 清空画布
          ctx.clearRect(0, 0, this.canvasWidth, actualHeight)

          // 设置背景色
          ctx.setFillStyle('#ffffff')
          ctx.fillRect(0, 0, this.canvasWidth, actualHeight)

          // 绘制海报内容 - 使用精确的DOM尺寸
          this.drawPosterContentWithDOMSizes(ctx, rect, actualHeight)

          // 执行绘制
          ctx.draw(false, () => {
            setTimeout(() => {
              uni.canvasToTempFilePath({
                canvasId: 'posterCanvas',
                width: this.canvasWidth,
                height: actualHeight,
                success: resolve,
                fail: reject
              }, this)
            }, 1000)
          })
        }).exec()
      })
    },

    // 绘制完整海报内容
    async drawPosterContentComplete(ctx, canvasHeight) {
      const padding = 40
      let currentY = padding

      try {
        // 绘制用户信息（包含头像）
        currentY = await this.drawUserSectionWithAvatar(ctx, currentY, padding)

        // 绘制标签
        if (this.tags && this.tags.length > 0) {
          currentY = this.drawTagsWithBackground(ctx, currentY, padding)
        }

        // 绘制标题
        currentY = this.drawTitleComplete(ctx, currentY, padding)

        // 绘制内容
        currentY = this.drawContentComplete(ctx, currentY, padding)

        // 绘制图片（如果有）
        if (this.showImages && this.displayImages.length > 0) {
          currentY = await this.drawImagesComplete(ctx, currentY, padding)
        }

        // 绘制统计信息
        currentY = this.drawStatsComplete(ctx, currentY, padding)

        // 绘制二维码和品牌信息
        this.drawFooterComplete(ctx, padding, canvasHeight)

      } catch (error) {
        console.error('绘制海报内容失败:', error)
        // 如果出错，使用简化版本
        this.drawPosterContent(ctx)
      }
    },

    // 绘制海报内容（简化版本，保持向后兼容）
    drawPosterContent(ctx) {
      const padding = 40
      let currentY = padding

      // 绘制用户信息
      currentY = this.drawUserSection(ctx, currentY, padding)

      // 绘制标签
      if (this.tags && this.tags.length > 0) {
        currentY = this.drawTags(ctx, currentY, padding)
      }

      // 绘制标题
      currentY = this.drawTitle(ctx, currentY, padding)

      // 绘制内容
      currentY = this.drawContent(ctx, currentY, padding)

      // 绘制统计信息
      currentY = this.drawStats(ctx, currentY, padding)

      // 绘制二维码和品牌信息
      this.drawFooter(ctx, padding)
    },

    // 绘制用户信息（包含头像）
    async drawUserSectionWithAvatar(ctx, startY, padding) {
      const avatarSize = 60
      const spacing = 20

      try {
        // 加载并绘制头像
        if (this.userInfo.avatar) {
          await this.drawImageToCanvas(ctx, this.userInfo.avatar, padding, startY, avatarSize, avatarSize, true)
        } else {
          // 绘制默认头像占位符
          ctx.setFillStyle('#f0f0f0')
          ctx.fillRect(padding, startY, avatarSize, avatarSize)
          ctx.setStrokeStyle('#e0e0e0')
          ctx.setLineWidth(1)
          ctx.strokeRect(padding, startY, avatarSize, avatarSize)
        }
      } catch (error) {
        console.error('绘制头像失败:', error)
        // 绘制默认头像占位符
        ctx.setFillStyle('#f0f0f0')
        ctx.fillRect(padding, startY, avatarSize, avatarSize)
      }

      // 绘制用户名
      ctx.setFillStyle('#333333')
      ctx.setFontSize(28)
      ctx.setTextAlign('left')
      ctx.fillText(this.userInfo.nickName, padding + avatarSize + spacing, startY + 25)

      // 绘制发布时间
      ctx.setFillStyle('#999999')
      ctx.setFontSize(22)
      ctx.fillText(this.publishTime, padding + avatarSize + spacing, startY + 55)

      // 绘制分割线
      ctx.setStrokeStyle('#f0f0f0')
      ctx.setLineWidth(1)
      ctx.beginPath()
      ctx.moveTo(padding, startY + avatarSize + 20)
      ctx.lineTo(this.canvasWidth - padding, startY + avatarSize + 20)
      ctx.stroke()

      return startY + avatarSize + 40
    },

    // 绘制用户信息（简化版本）
    drawUserSection(ctx, startY, padding) {
      const avatarSize = 60
      const spacing = 20

      // 绘制用户名
      ctx.setFillStyle('#333333')
      ctx.setFontSize(28)
      ctx.setTextAlign('left')
      ctx.fillText(this.userInfo.nickName, padding + avatarSize + spacing, startY + 25)

      // 绘制发布时间
      ctx.setFillStyle('#999999')
      ctx.setFontSize(22)
      ctx.fillText(this.publishTime, padding + avatarSize + spacing, startY + 55)

      return startY + avatarSize + 30
    },

    // 绘制带背景的标签
    drawTagsWithBackground(ctx, startY, padding) {
      if (!this.tags || this.tags.length === 0) return startY

      let currentX = padding
      let currentRowY = startY
      const tagHeight = 32
      const tagSpacing = 12
      const rowSpacing = 10
      const tagPadding = 16

      ctx.setFontSize(22)

      this.tags.forEach(tag => {
        const tagText = `#${tag}`
        const textWidth = ctx.measureText(tagText).width
        const tagWidth = textWidth + tagPadding * 2

        // 检查是否需要换行
        if (currentX + tagWidth > this.canvasWidth - padding) {
          currentX = padding
          currentRowY += tagHeight + rowSpacing
        }

        // 绘制标签背景
        ctx.setFillStyle('#e3f2fd')
        this.drawRoundedRect(ctx, currentX, currentRowY, tagWidth, tagHeight, 16)
        ctx.fill()

        // 绘制标签文字
        ctx.setFillStyle('#1976d2')
        ctx.setTextAlign('left')
        ctx.fillText(tagText, currentX + tagPadding, currentRowY + 22)

        currentX += tagWidth + tagSpacing
      })

      return currentRowY + tagHeight + 30
    },

    // 绘制标签（简化版本）
    drawTags(ctx, startY, padding) {
      if (!this.tags || this.tags.length === 0) return startY

      let currentX = padding
      const tagHeight = 35
      const tagSpacing = 15

      ctx.setFillStyle('#007AFF')
      ctx.setFontSize(22)

      this.tags.forEach(tag => {
        const tagText = `#${tag}`
        const textWidth = ctx.measureText(tagText).width
        const tagWidth = textWidth + 20

        // 绘制标签文字
        ctx.fillText(tagText, currentX, startY + 25)
        currentX += tagWidth + tagSpacing
      })

      return startY + tagHeight + 20
    },

    // 绘制完整标题
    drawTitleComplete(ctx, startY, padding) {
      ctx.setFillStyle('#333333')
      ctx.setFontSize(36)
      ctx.setTextAlign('left')

      const lines = this.wrapText(ctx, this.title, this.canvasWidth - padding * 2)
      const lineHeight = 50

      lines.forEach((line, index) => {
        ctx.fillText(line, padding, startY + (index + 1) * lineHeight)
      })

      return startY + lines.length * lineHeight + 30
    },

    // 绘制完整内容
    drawContentComplete(ctx, startY, padding) {
      // 去除HTML标签并保留换行
      let plainText = this.content.replace(/<[^>]*>/g, '')
      plainText = plainText.replace(/&nbsp;/g, ' ').replace(/&lt;/g, '<').replace(/&gt;/g, '>')

      const maxLength = 800 // 增加内容长度限制
      const displayText = plainText.length > maxLength ?
        plainText.substring(0, maxLength) + '...' : plainText

      ctx.setFillStyle('#333333')
      ctx.setFontSize(28)
      ctx.setTextAlign('left')

      const lines = this.wrapText(ctx, displayText, this.canvasWidth - padding * 2)
      const lineHeight = 42
      const maxLines = 20 // 增加行数限制

      const displayLines = lines.slice(0, maxLines)
      displayLines.forEach((line, index) => {
        ctx.fillText(line, padding, startY + (index + 1) * lineHeight)
      })

      // 如果内容被截断，添加提示
      if (lines.length > maxLines || plainText.length > maxLength) {
        ctx.setFillStyle('#999999')
        ctx.setFontSize(24)
        ctx.fillText('...查看完整内容请扫描下方二维码', padding, startY + (displayLines.length + 1) * lineHeight + 20)
        return startY + (displayLines.length + 2) * lineHeight + 40
      }

      return startY + displayLines.length * lineHeight + 40
    },

    // 绘制标题（简化版本）
    drawTitle(ctx, startY, padding) {
      ctx.setFillStyle('#000000')
      ctx.setFontSize(36)
      ctx.setTextAlign('left')

      const lines = this.wrapText(ctx, this.title, this.canvasWidth - padding * 2)
      const lineHeight = 50

      lines.forEach((line, index) => {
        ctx.fillText(line, padding, startY + (index + 1) * lineHeight)
      })

      return startY + lines.length * lineHeight + 30
    },

    // 绘制内容（简化版本）
    drawContent(ctx, startY, padding) {
      // 去除HTML标签
      const plainText = this.content.replace(/<[^>]*>/g, '')
      const maxLength = 400
      const displayText = plainText.length > maxLength ?
        plainText.substring(0, maxLength) + '...' : plainText

      ctx.setFillStyle('#333333')
      ctx.setFontSize(28)

      const lines = this.wrapText(ctx, displayText, this.canvasWidth - padding * 2)
      const lineHeight = 40
      const maxLines = 12

      const displayLines = lines.slice(0, maxLines)
      displayLines.forEach((line, index) => {
        ctx.fillText(line, padding, startY + (index + 1) * lineHeight)
      })

      return startY + displayLines.length * lineHeight + 40
    },

    // 绘制完整图片
    async drawImagesComplete(ctx, startY, padding) {
      if (!this.displayImages || this.displayImages.length === 0) return startY

      const imageSpacing = 10
      const maxImagesPerRow = 3
      const availableWidth = this.canvasWidth - padding * 2
      const imageWidth = (availableWidth - imageSpacing * (maxImagesPerRow - 1)) / maxImagesPerRow
      const imageHeight = imageWidth * 0.75 // 4:3 比例

      let currentX = padding
      let currentY = startY
      let imagesInCurrentRow = 0

      for (let i = 0; i < this.displayImages.length; i++) {
        const image = this.displayImages[i]

        try {
          // 绘制图片
          await this.drawImageToCanvas(ctx, image, currentX, currentY, imageWidth, imageHeight, false)
        } catch (error) {
          console.error('绘制图片失败:', error)
          // 绘制占位符
          ctx.setFillStyle('#f5f5f5')
          ctx.fillRect(currentX, currentY, imageWidth, imageHeight)
          ctx.setStrokeStyle('#e0e0e0')
          ctx.setLineWidth(1)
          ctx.strokeRect(currentX, currentY, imageWidth, imageHeight)

          // 绘制图片图标
          ctx.setFillStyle('#cccccc')
          ctx.setFontSize(24)
          ctx.setTextAlign('center')
          ctx.fillText('图片', currentX + imageWidth / 2, currentY + imageHeight / 2)
        }

        imagesInCurrentRow++
        currentX += imageWidth + imageSpacing

        // 换行处理
        if (imagesInCurrentRow >= maxImagesPerRow || i === this.displayImages.length - 1) {
          currentY += imageHeight + imageSpacing
          currentX = padding
          imagesInCurrentRow = 0
        }
      }

      return currentY + 20
    },

    // 绘制完整统计信息
    drawStatsComplete(ctx, startY, padding) {
      // 绘制分割线
      ctx.setStrokeStyle('#f0f0f0')
      ctx.setLineWidth(1)
      ctx.beginPath()
      ctx.moveTo(padding, startY)
      ctx.lineTo(this.canvasWidth - padding, startY)
      ctx.stroke()

      const statsY = startY + 30

      ctx.setFillStyle('#666666')
      ctx.setFontSize(24)
      ctx.setTextAlign('left')
      ctx.fillText(this.templateType, padding, statsY)

      ctx.setTextAlign('right')
      ctx.fillText(`阅读 ${this.clickCount} 次`, this.canvasWidth - padding, statsY)

      return statsY + 50
    },

    // 绘制统计信息（简化版本）
    drawStats(ctx, startY, padding) {
      // 绘制分割线
      ctx.setStrokeStyle('#f0f0f0')
      ctx.setLineWidth(2)
      ctx.beginPath()
      ctx.moveTo(padding, startY)
      ctx.lineTo(this.canvasWidth - padding, startY)
      ctx.stroke()

      const statsY = startY + 30

      ctx.setFillStyle('#666666')
      ctx.setFontSize(24)
      ctx.setTextAlign('left')
      ctx.fillText(this.templateType, padding, statsY)

      ctx.setTextAlign('right')
      ctx.fillText(`阅读 ${this.clickCount} 次`, this.canvasWidth - padding, statsY)

      return statsY + 50
    },

    // 绘制完整底部信息
    drawFooterComplete(ctx, padding, canvasHeight) {
      const footerY = canvasHeight - 140

      // 绘制分割线
      ctx.setStrokeStyle('#f0f0f0')
      ctx.setLineWidth(1)
      ctx.beginPath()
      ctx.moveTo(padding, footerY - 20)
      ctx.lineTo(this.canvasWidth - padding, footerY - 20)
      ctx.stroke()

      // 绘制二维码占位符
      const qrSize = 80
      const qrX = this.canvasWidth - padding - qrSize

      ctx.setFillStyle('#f5f5f5')
      ctx.fillRect(qrX, footerY, qrSize, qrSize)

      ctx.setStrokeStyle('#e0e0e0')
      ctx.setLineWidth(1)
      ctx.strokeRect(qrX, footerY, qrSize, qrSize)

      // 绘制简单的二维码图案
      this.drawSimpleQRCode(ctx, qrX, footerY, qrSize)

      // 绘制二维码说明
      ctx.setFillStyle('#666666')
      ctx.setFontSize(20)
      ctx.setTextAlign('center')
      ctx.fillText('扫码查看完整内容', qrX + qrSize / 2, footerY + qrSize + 25)

      // 绘制品牌信息
      ctx.setFillStyle('#999999')
      ctx.setFontSize(22)
      ctx.setTextAlign('left')
      ctx.fillText('案例管理系统', padding, footerY + qrSize - 10)
    },

    // 绘制底部信息（简化版本）
    drawFooter(ctx, padding) {
      const footerY = this.canvasHeight - 120

      // 绘制二维码占位符
      const qrSize = 80
      const qrX = this.canvasWidth - padding - qrSize

      ctx.setFillStyle('#f5f5f5')
      ctx.fillRect(qrX, footerY, qrSize, qrSize)

      ctx.setStrokeStyle('#e0e0e0')
      ctx.setLineWidth(2)
      ctx.strokeRect(qrX, footerY, qrSize, qrSize)

      // 绘制二维码说明
      ctx.setFillStyle('#666666')
      ctx.setFontSize(20)
      ctx.setTextAlign('center')
      ctx.fillText('扫码查看', qrX + qrSize / 2, footerY + qrSize + 25)

      // 绘制品牌信息
      ctx.setFillStyle('#999999')
      ctx.setFontSize(22)
      ctx.setTextAlign('left')
      ctx.fillText('案例管理系统', padding, footerY + qrSize - 10)
    },

    // 绘制图片到Canvas
    drawImageToCanvas(ctx, imageSrc, x, y, width, height, isCircle = false) {
      return new Promise((resolve, reject) => {
        // 在uniapp中，需要先下载网络图片到本地
        if (imageSrc.startsWith('http')) {
          uni.downloadFile({
            url: imageSrc,
            success: (res) => {
              if (res.statusCode === 200) {
                this.drawLocalImage(ctx, res.tempFilePath, x, y, width, height, isCircle)
                  .then(resolve)
                  .catch(reject)
              } else {
                reject(new Error('图片下载失败'))
              }
            },
            fail: reject
          })
        } else {
          this.drawLocalImage(ctx, imageSrc, x, y, width, height, isCircle)
            .then(resolve)
            .catch(reject)
        }
      })
    },

    // 绘制本地图片
    drawLocalImage(ctx, imagePath, x, y, width, height, isCircle = false) {
      return new Promise((resolve, reject) => {
        if (isCircle) {
          // 绘制圆形图片（头像）
          ctx.save()
          ctx.beginPath()
          ctx.arc(x + width / 2, y + height / 2, width / 2, 0, 2 * Math.PI)
          ctx.clip()
          ctx.drawImage(imagePath, x, y, width, height)
          ctx.restore()
        } else {
          // 绘制矩形图片
          ctx.drawImage(imagePath, x, y, width, height)
        }
        resolve()
      })
    },

    // 绘制圆角矩形
    drawRoundedRect(ctx, x, y, width, height, radius) {
      ctx.beginPath()
      ctx.moveTo(x + radius, y)
      ctx.lineTo(x + width - radius, y)
      ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
      ctx.lineTo(x + width, y + height - radius)
      ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
      ctx.lineTo(x + radius, y + height)
      ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
      ctx.lineTo(x, y + radius)
      ctx.quadraticCurveTo(x, y, x + radius, y)
      ctx.closePath()
    },

    // 绘制简单二维码图案
    drawSimpleQRCode(ctx, x, y, size) {
      ctx.setFillStyle('#000000')
      const blockSize = 3

      // 绘制随机点阵
      for (let i = 0; i < size; i += blockSize) {
        for (let j = 0; j < size; j += blockSize) {
          if (Math.random() > 0.6) {
            ctx.fillRect(x + i, y + j, blockSize, blockSize)
          }
        }
      }

      // 绘制定位点
      const cornerSize = 16
      // 左上角
      ctx.fillRect(x, y, cornerSize, cornerSize)
      ctx.setFillStyle('#ffffff')
      ctx.fillRect(x + 3, y + 3, cornerSize - 6, cornerSize - 6)
      ctx.setFillStyle('#000000')
      ctx.fillRect(x + 6, y + 6, cornerSize - 12, cornerSize - 12)

      // 右上角
      ctx.setFillStyle('#000000')
      ctx.fillRect(x + size - cornerSize, y, cornerSize, cornerSize)
      ctx.setFillStyle('#ffffff')
      ctx.fillRect(x + size - cornerSize + 3, y + 3, cornerSize - 6, cornerSize - 6)
      ctx.setFillStyle('#000000')
      ctx.fillRect(x + size - cornerSize + 6, y + 6, cornerSize - 12, cornerSize - 12)

      // 左下角
      ctx.setFillStyle('#000000')
      ctx.fillRect(x, y + size - cornerSize, cornerSize, cornerSize)
      ctx.setFillStyle('#ffffff')
      ctx.fillRect(x + 3, y + size - cornerSize + 3, cornerSize - 6, cornerSize - 6)
      ctx.setFillStyle('#000000')
      ctx.fillRect(x + 6, y + size - cornerSize + 6, cornerSize - 12, cornerSize - 12)
    },

    // DOM转图片 - 用于1:1还原
    convertDOMToImage(rect) {
      return new Promise((resolve, reject) => {
        // 在小程序环境中，我们需要使用不同的方法
        // #ifdef MP-WEIXIN
        // 微信小程序不支持直接DOM截图，使用Canvas绘制
        reject(new Error('微信小程序不支持DOM截图'))
        // #endif

        // #ifdef H5
        // H5环境可以使用html2canvas等库
        if (typeof html2canvas !== 'undefined') {
          const element = document.querySelector(`#poster-preview-${this.caseId}`)
          html2canvas(element).then(canvas => {
            resolve(canvas.toDataURL())
          }).catch(reject)
        } else {
          reject(new Error('H5环境需要html2canvas库'))
        }
        // #endif

        // #ifndef MP-WEIXIN || H5
        reject(new Error('当前平台不支持DOM截图'))
        // #endif
      })
    },

    // 使用DOM尺寸精确绘制Canvas
    async drawPosterContentWithDOMSizes(ctx, domRect, canvasHeight) {
      try {
        // 获取所有子元素的精确位置和尺寸
        const elementsInfo = await this.getDOMElementsInfo()

        // 根据实际DOM布局绘制内容
        await this.drawElementsFromDOM(ctx, elementsInfo)

      } catch (error) {
        console.error('使用DOM尺寸绘制失败，使用默认方法:', error)
        // 降级到原有方法
        this.drawPosterContentComplete(ctx, canvasHeight)
      }
    },

    // 获取DOM元素信息
    getDOMElementsInfo() {
      return new Promise((resolve, reject) => {
        const query = uni.createSelectorQuery().in(this)

        // 查询所有关键元素的位置和尺寸
        query.select('.user-section').boundingClientRect()
        query.select('.user-avatar').boundingClientRect()
        query.select('.user-name').boundingClientRect()
        query.select('.publish-time').boundingClientRect()
        query.select('.case-tags').boundingClientRect()
        query.select('.case-title').boundingClientRect()
        query.select('.case-content').boundingClientRect()
        query.select('.case-images').boundingClientRect()
        query.select('.stats-section').boundingClientRect()
        query.select('.qr-section').boundingClientRect()
        query.select('.brand-section').boundingClientRect()

        query.exec((res) => {
          if (res && res.length > 0) {
            const elementsInfo = {
              userSection: res[0],
              userAvatar: res[1],
              userName: res[2],
              publishTime: res[3],
              caseTags: res[4],
              caseTitle: res[5],
              caseContent: res[6],
              caseImages: res[7],
              statsSection: res[8],
              qrSection: res[9],
              brandSection: res[10]
            }
            resolve(elementsInfo)
          } else {
            reject(new Error('无法获取DOM元素信息'))
          }
        })
      })
    },

    // 根据DOM信息绘制元素
    async drawElementsFromDOM(ctx, elementsInfo) {
      const baseRect = elementsInfo.userSection // 使用用户区域作为基准
      if (!baseRect) return

      // 计算rpx到px的转换比例
      const rpxToPx = uni.getSystemInfoSync().windowWidth / 750

      // 绘制用户头像
      if (elementsInfo.userAvatar) {
        const avatar = elementsInfo.userAvatar
        const x = (avatar.left - baseRect.left) / rpxToPx
        const y = (avatar.top - baseRect.top) / rpxToPx
        const size = avatar.width / rpxToPx

        try {
          await this.drawImageToCanvas(ctx, this.userInfo.avatar, x, y, size, size, true)
        } catch (error) {
          // 绘制占位符
          ctx.setFillStyle('#f0f0f0')
          ctx.fillRect(x, y, size, size)
        }
      }

      // 绘制用户名
      if (elementsInfo.userName) {
        const userName = elementsInfo.userName
        const x = (userName.left - baseRect.left) / rpxToPx
        const y = (userName.top - baseRect.top) / rpxToPx + userName.height / rpxToPx * 0.7

        ctx.setFillStyle('#333333')
        ctx.setFontSize(28)
        ctx.setTextAlign('left')
        ctx.fillText(this.userInfo.nickName, x, y)
      }

      // 绘制发布时间
      if (elementsInfo.publishTime) {
        const publishTime = elementsInfo.publishTime
        const x = (publishTime.left - baseRect.left) / rpxToPx
        const y = (publishTime.top - baseRect.top) / rpxToPx + publishTime.height / rpxToPx * 0.7

        ctx.setFillStyle('#999999')
        ctx.setFontSize(22)
        ctx.setTextAlign('left')
        ctx.fillText(this.publishTime, x, y)
      }

      // 绘制标签
      if (elementsInfo.caseTags && this.tags && this.tags.length > 0) {
        const tagsRect = elementsInfo.caseTags
        const startX = (tagsRect.left - baseRect.left) / rpxToPx
        const startY = (tagsRect.top - baseRect.top) / rpxToPx

        this.drawTagsAtPosition(ctx, startX, startY)
      }

      // 绘制标题
      if (elementsInfo.caseTitle) {
        const titleRect = elementsInfo.caseTitle
        const x = (titleRect.left - baseRect.left) / rpxToPx
        const y = (titleRect.top - baseRect.top) / rpxToPx
        const width = titleRect.width / rpxToPx

        this.drawTitleAtPosition(ctx, x, y, width)
      }

      // 绘制内容
      if (elementsInfo.caseContent) {
        const contentRect = elementsInfo.caseContent
        const x = (contentRect.left - baseRect.left) / rpxToPx
        const y = (contentRect.top - baseRect.top) / rpxToPx
        const width = contentRect.width / rpxToPx

        this.drawContentAtPosition(ctx, x, y, width)
      }
    },

    // 在指定位置绘制标签
    drawTagsAtPosition(ctx, startX, startY) {
      if (!this.tags || this.tags.length === 0) return

      let currentX = startX
      const tagHeight = 32
      const tagSpacing = 12
      const tagPadding = 16

      ctx.setFontSize(22)

      this.tags.forEach(tag => {
        const tagText = `#${tag}`
        const textWidth = ctx.measureText(tagText).width
        const tagWidth = textWidth + tagPadding * 2

        // 绘制标签背景
        ctx.setFillStyle('#f0f8ff')
        this.drawRoundedRect(ctx, currentX, startY, tagWidth, tagHeight, 16)
        ctx.fill()

        // 绘制标签文字
        ctx.setFillStyle('#007AFF')
        ctx.setTextAlign('left')
        ctx.fillText(tagText, currentX + tagPadding, startY + 22)

        currentX += tagWidth + tagSpacing
      })
    },

    // 在指定位置绘制标题
    drawTitleAtPosition(ctx, x, y, width) {
      ctx.setFillStyle('#333333')
      ctx.setFontSize(36)
      ctx.setTextAlign('left')

      const lines = this.wrapText(ctx, this.title, width)
      const lineHeight = 50

      lines.forEach((line, index) => {
        ctx.fillText(line, x, y + (index + 1) * lineHeight)
      })
    },

    // 在指定位置绘制内容
    drawContentAtPosition(ctx, x, y, width) {
      const plainText = this.content.replace(/<[^>]*>/g, '')

      ctx.setFillStyle('#666666')
      ctx.setFontSize(30)
      ctx.setTextAlign('left')

      const lines = this.wrapText(ctx, plainText, width)
      const lineHeight = 42

      lines.forEach((line, index) => {
        ctx.fillText(line, x, y + (index + 1) * lineHeight)
      })
    },

    // 文字换行处理
    wrapText(ctx, text, maxWidth) {
      const words = text.split('')
      const lines = []
      let currentLine = ''

      for (let i = 0; i < words.length; i++) {
        const testLine = currentLine + words[i]
        const metrics = ctx.measureText(testLine)
        const testWidth = metrics.width

        if (testWidth > maxWidth && i > 0) {
          lines.push(currentLine)
          currentLine = words[i]
        } else {
          currentLine = testLine
        }
      }
      lines.push(currentLine)
      return lines
    }
  }
}
</script>

<style scoped>
.poster-generator {
  position: relative;
}

.poster-preview {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
  margin: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.poster-content {
  background-color: #ffffff;
  padding: 40rpx;
  position: relative;
  min-height: 1000rpx;
  display: flex;
  flex-direction: column;
}

/* 用户信息 */
.user-section {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f0f0f0;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.publish-time {
  font-size: 26rpx;
  color: #999;
}

/* 标签 */
.case-tags {
  margin-bottom: 20rpx;
}

.tag {
  display: inline-block;
  background-color: #f0f8ff;
  color: #007AFF;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  margin-right: 12rpx;
  margin-bottom: 10rpx;
}

/* 标题 */
.case-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 30rpx;
}

/* 内容 */
.case-content {
  font-size: 30rpx;
  color: #666;
  line-height: 1.8;
  margin-bottom: 30rpx;
  flex: 1;
}

/* 图片 */
.case-images {
  margin-bottom: 30rpx;
}

.image-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.image-grid.grid-1 .image-item {
  width: 100%;
}

.image-grid.grid-2 .image-item {
  width: calc(50% - 10rpx);
}

.image-grid.grid-3 .image-item {
  width: calc(33.33% - 14rpx);
}

.image-item {
  border-radius: 12rpx;
  overflow: hidden;
}

.case-image {
  width: 100%;
  display: block;
  border-radius: 12rpx;
}

/* 统计信息 */
.stats-section {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-label {
  font-size: 28rpx;
  color: #666;
}

/* 二维码区域 */
.qr-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.qr-text {
  font-size: 24rpx;
  color: #666;
}

.qr-placeholder {
  width: 80rpx;
  height: 80rpx;
  background-color: #f5f5f5;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-icon {
  font-size: 40rpx;
  color: #ccc;
}

/* 品牌标识 */
.brand-section {
  text-align: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
  margin-top: auto;
}

.brand-text {
  font-size: 22rpx;
  color: #999;
}

/* 隐藏的canvas */
.hidden-canvas {
  position: fixed;
  top: -9999px;
  left: -9999px;
  opacity: 0;
  pointer-events: none;
}
</style>
