<template>
  <view class="image-viewer" v-if="visible" @tap="closeViewer">
    <!-- 背景遮罩 -->
    <view class="viewer-mask"></view>
    
    <!-- 图片容器 -->
    <view class="viewer-content" @tap.stop="">
      <!-- 关闭按钮 -->
      <view class="close-btn" @tap="closeViewer">
        <text class="close-icon">×</text>
      </view>
      
      <!-- 图片计数 -->
      <view class="image-counter" v-if="images.length > 1">
        <text class="counter-text">{{ currentIndex + 1 }} / {{ images.length }}</text>
      </view>
      
      <!-- 滑动容器 -->
      <swiper 
        class="image-swiper" 
        :current="currentIndex" 
        @change="onSwiperChange"
        :indicator-dots="false"
        :autoplay="false"
        :circular="false"
      >
        <swiper-item v-for="(image, index) in images" :key="index" class="swiper-item">
          <view class="image-container">
            <image 
              class="preview-image" 
              :src="image" 
              mode="aspectFit"
              @error="onImageError"
              @load="onImageLoad"
            />
          </view>
        </swiper-item>
      </swiper>
      
      <!-- 左右切换按钮 -->
      <view class="nav-buttons" v-if="images.length > 1">
        <view 
          class="nav-btn nav-prev" 
          :class="{ disabled: currentIndex === 0 }"
          @tap="prevImage"
        >
          <text class="nav-icon">‹</text>
        </view>
        <view 
          class="nav-btn nav-next" 
          :class="{ disabled: currentIndex === images.length - 1 }"
          @tap="nextImage"
        >
          <text class="nav-icon">›</text>
        </view>
      </view>
      
      <!-- 缩略图导航 -->
      <view class="thumbnail-nav" v-if="images.length > 1 && showThumbnails">
        <scroll-view class="thumbnail-scroll" scroll-x="true" :scroll-left="thumbnailScrollLeft">
          <view class="thumbnail-list">
            <view 
              class="thumbnail-item" 
              :class="{ active: index === currentIndex }"
              v-for="(image, index) in images" 
              :key="index"
              @tap="goToImage(index)"
            >
              <image class="thumbnail-image" :src="image" mode="aspectFill" />
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ImageViewer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    images: {
      type: Array,
      default: () => []
    },
    initialIndex: {
      type: Number,
      default: 0
    },
    showThumbnails: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      currentIndex: 0,
      thumbnailScrollLeft: 0
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.currentIndex = this.initialIndex
        this.updateThumbnailScroll()
      }
    },
    initialIndex(newVal) {
      this.currentIndex = newVal
      this.updateThumbnailScroll()
    }
  },
  methods: {
    closeViewer() {
      this.$emit('close')
    },
    
    onSwiperChange(e) {
      this.currentIndex = e.detail.current
      this.updateThumbnailScroll()
      this.$emit('change', this.currentIndex)
    },
    
    prevImage() {
      if (this.currentIndex > 0) {
        this.currentIndex--
        this.updateThumbnailScroll()
        this.$emit('change', this.currentIndex)
      }
    },
    
    nextImage() {
      if (this.currentIndex < this.images.length - 1) {
        this.currentIndex++
        this.updateThumbnailScroll()
        this.$emit('change', this.currentIndex)
      }
    },
    
    goToImage(index) {
      this.currentIndex = index
      this.updateThumbnailScroll()
      this.$emit('change', this.currentIndex)
    },
    
    updateThumbnailScroll() {
      // 计算缩略图滚动位置，确保当前图片在可视区域内
      const itemWidth = 120 // 缩略图宽度 + 间距
      const containerWidth = 300 // 大概的容器宽度
      const scrollLeft = Math.max(0, this.currentIndex * itemWidth - containerWidth / 2)
      this.thumbnailScrollLeft = scrollLeft
    },
    
    onImageError(e) {
      console.warn('图片加载失败:', e)
      uni.showToast({
        title: '图片加载失败',
        icon: 'none'
      })
    },
    
    onImageLoad(e) {
      console.log('图片加载成功:', e)
    }
  }
}
</script>

<style scoped>
.image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.viewer-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
}

.viewer-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.close-btn {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.close-icon {
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
}

.image-counter {
  position: absolute;
  top: 40rpx;
  left: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  z-index: 10;
}

.counter-text {
  color: #fff;
  font-size: 28rpx;
}

.image-swiper {
  flex: 1;
  width: 100%;
}

.swiper-item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
}

.nav-buttons {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 40rpx;
  pointer-events: none;
}

.nav-btn {
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
}

.nav-btn.disabled {
  opacity: 0.3;
  pointer-events: none;
}

.nav-icon {
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
}

.thumbnail-nav {
  position: absolute;
  bottom: 40rpx;
  left: 0;
  right: 0;
  height: 120rpx;
}

.thumbnail-scroll {
  height: 100%;
  white-space: nowrap;
}

.thumbnail-list {
  display: inline-flex;
  padding: 0 40rpx;
}

.thumbnail-item {
  width: 100rpx;
  height: 100rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
  overflow: hidden;
  border: 4rpx solid transparent;
}

.thumbnail-item.active {
  border-color: #007aff;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
}
</style>
